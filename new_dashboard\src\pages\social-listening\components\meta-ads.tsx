import image from '../images/integrations/meta_ads_logo.png';
import Card from './Card';
import endPoints from '../apis/agent';
import LoaderModal from '../../../components/modals/LoaderModal';
import { useEffect, useState } from 'react';
import { useApiMutation, useApiQuery } from '../../../hooks/react-query-hooks';
import { channelNames } from '../utils/constant';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { AuthUser } from '../../../types/auth';
import { connectToMetaAdsSentiment } from '../utils';
import { dialogMessage } from '../../../utils/strings/content-manager';
import { ApiError } from './facebook-ads-form';
import { metaAdsAccounts } from '../apis/agent';
import { useToast } from '@chakra-ui/react';
import { showMultiSelectModal } from '../utils/modal-helpers';
import Swal from 'sweetalert2';

const MetaAds = () => {
   const [isDisconnecting, setIsDisconnecting] = useState(false);
   const toast = useToast();
   const authUser = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   );

   const showToast = (
      title: string,
      description: string,
      status: 'success' | 'error',
   ) => {
      toast({ title, description, status, duration: 2000, isClosable: true });
   };

   const { data, isLoading, errorMessage } = useApiQuery({
      queryKey: [`metaAdsConnectionDetails`],
      queryFn: () =>
         endPoints.checkConnectionDetails({
            client_id: authUser?.client_id || '',
            channel_name: channelNames.META_ADS,
         }),
   });

   const {
      mutateAsync: fetchCredentials,
      //isPending: isFetchingCredentials,
      //data: gaResponse,
      //errorMessage: gaErrorMessage,
   } = useApiMutation<{ access_token: string }, { client_id: string }>({
      mutationFn: endPoints.fetchMetaCredentials,
   });

   const {
      mutateAsync: fetchMetaAdsAccounts,
      isPending: isFetchingMetaAdsAccounts,
      //data: gaResponse,
      //errorMessage: gaErrorMessage,
   } = useApiMutation<
      metaAdsAccounts[],
      { clientId: string; userAccessToken: string }
   >({
      mutationFn: endPoints.getMetaAdsAccounts,
   });

   const {
      mutateAsync: saveSelectedMetaAccounts,
      // isPending: isSavingGAProperties,
      //data: gaResponse,
      //errorMessage: gaErrorMessage,
   } = useApiMutation({
      mutationFn: endPoints.saveMetaAdsSelections,
   });

   const { mutate: connectToSentiment, isPending: isConnectingToSentiment } =
      useApiMutation({
         mutationFn: connectToMetaAdsSentiment,

         onSuccessHandler: async (_data, payload) => {
            if (!payload?.isConnect) {
               setTimeout(() => {
                  window.location.href = `${window.location.origin}/integrations`;
               }, 1000);
               return;
            }
            try {
               if (!authUser?.client_id) {
                  showToast('Error', 'Client ID missing', 'error');
                  return;
               }

               const credentialsData = await fetchCredentials({
                  client_id: authUser?.client_id,
               });
               const { access_token: userToken } = credentialsData;

               const gaAccounts: metaAdsAccounts[] = await fetchMetaAdsAccounts(
                  {
                     clientId: authUser?.client_id,
                     userAccessToken: userToken,
                  },
               );

               const selectAccountOptions: Record<string, string> = {};
               gaAccounts.forEach((account) => {
                  selectAccountOptions[account.id] =
                     `${account.name}, ${account.id}`;
               });

               const selectedMetaAccounts = await showMultiSelectModal(
                  'Select Meta Ads Accounts',
                  selectAccountOptions,
               );
               if (!selectedMetaAccounts) {
                  connectToSentiment({
                     client_id: authUser?.client_id,
                     isConnect: false,
                  });
                  return;
               }
               if (selectedMetaAccounts) {
                  await saveSelectedMetaAccounts({
                     clientId: authUser?.client_id,
                     selectedAccounts: selectedMetaAccounts,
                  });
               }

               showToast('Success', 'Connected to Meta Ads', 'success');

               setTimeout(() => {
                  window.location.href = `${window.location.origin}/integrations`;
               }, 1000);
            } catch (error) {
               showToast(
                  'Warning',
                  'something went wrong, please try again.',
                  'error',
               );
               console.error('Failed to fetch Meta Ads properties:', error);
            }
         },
      });

   const { is_active = false } = data?.details || {};

   async function onConnect() {
      const {
         data: { url },
      } = await endPoints.getMetaAdsAuthUrl();
      window.location.href = url;
   }

   const onDisconnect = async () => {
      const result = await Swal.fire({
         title: dialogMessage.delete.title,
         text: dialogMessage.delete.description,
         icon: 'warning',
         showCancelButton: true,
         confirmButtonColor: '#3085d6',
         cancelButtonColor: '#d33',
         confirmButtonText: dialogMessage.delete.buttonMessage,
      });
      if (result.isConfirmed && authUser?.client_id) {
         try {
            setIsDisconnecting(true);
            connectToSentiment({
               client_id: authUser?.client_id,
               isConnect: false,
            });
         } catch (err) {
            const error = err as ApiError;
            const msg = error.response.data.message;
            showToast('Could not disconnect', msg!, 'error');
         } finally {
            setIsDisconnecting(false);
         }
      }
   };

   function onClick() {
      is_active ? void onDisconnect() : void onConnect();
   }

   useEffect(() => {
      const searchParams = new URLSearchParams(window.location.search);
      const meta = searchParams.get('meta');
      const accessToken = searchParams.get('t');
      //  const refreshToken = searchParams.get('r');

      if (meta && accessToken && authUser?.client_id) {
         connectToSentiment({
            client_id: authUser.client_id,
            isConnect: true,
            access_token: accessToken,
         });
      }
   }, []);

   return (
      <>
         {isFetchingMetaAdsAccounts && <LoaderModal />}
         <Card
            error={errorMessage}
            isConnected={is_active}
            isDisconnecting={isDisconnecting}
            isConnecting={isConnectingToSentiment}
            isFetching={isLoading}
            onButtonClick={onClick}
            heading='Meta Ads'
            src={image}
         />
      </>
   );
};

export default MetaAds;
