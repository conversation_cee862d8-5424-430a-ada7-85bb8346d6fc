import React from 'react';
import { <PERSON>ader2, <PERSON><PERSON>ircle2, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { cn } from '@/utils';

interface FormFieldProps {
   id: string;
   name: string;
   type: string;
   label: string;
   placeholder: string;
   value: string;
   onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
   required?: boolean;
   disabled?: boolean;
   className?: string;
}

interface ApiResponseProps {
   success: boolean;
   message: string;
}

interface ModernSellerPanelProps {
   title: string;
   description: string;
   access_token: string;
   secret_key: string;
   onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
   onSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
   isLoading: boolean;
   apiResponse: ApiResponseProps | null;
   submitButtonText?: string;
   loadingText?: string;
}

export const ModernFormField: React.FC<FormFieldProps> = ({
   id,
   name,
   type,
   label,
   placeholder,
   value,
   onChange,
   required = false,
   disabled = false,
   className,
}) => {
   return (
      <div className={cn('space-y-2', className)}>
         <Label htmlFor={id} className='text-sm font-medium text-gray-700'>
            {label}
            {required && <span className='text-red-500 ml-1'>*</span>}
         </Label>
         <Input
            id={id}
            name={name}
            type={type}
            placeholder={placeholder}
            value={value}
            onChange={onChange}
            required={required}
            disabled={disabled}
            className='h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500'
         />
      </div>
   );
};

export const ModernApiResponse: React.FC<{ response: ApiResponseProps }> = ({
   response,
}) => {
   if (!response.message) return null;

   return (
      <Alert
         className={cn(
            'border-l-4',
            response.success
               ? 'border-l-green-500 bg-green-50 text-green-800'
               : 'border-l-red-500 bg-red-50 text-red-800',
         )}
      >
         {response.success ? (
            <CheckCircle2 className='h-4 w-4' />
         ) : (
            <AlertCircle className='h-4 w-4' />
         )}
         <AlertDescription className='font-medium'>
            {response.message}
         </AlertDescription>
      </Alert>
   );
};

export const IThinkLogisticsSellerPanel: React.FC<ModernSellerPanelProps> = ({
   title,
   description,
   access_token,
   secret_key,
   onChange,
   onSubmit,
   isLoading,
   apiResponse,
   submitButtonText = 'Connect',
   loadingText = 'Connecting...',
}) => {
   return (
      <div className='space-y-6'>
         <div className='space-y-2'>
            <h3 className='text-lg font-semibold text-gray-900'>{title}</h3>
            <p className='text-sm text-gray-600'>{description}</p>
         </div>

         <form onSubmit={onSubmit} className='space-y-6'>
            <div className='space-y-4'>
               <ModernFormField
                  id='access-token'
                  name='access_token'
                  type='password'
                  label='API Access Token'
                  placeholder='Enter your access token...'
                  value={access_token}
                  onChange={onChange}
                  required
                  disabled={isLoading}
               />

               <ModernFormField
                  id='secret-key'
                  name='secret_key'
                  type='password'
                  label='Secret Key'
                  placeholder='Enter your secret key...'
                  value={secret_key}
                  onChange={onChange}
                  required
                  disabled={isLoading}
               />
            </div>

            {apiResponse && <ModernApiResponse response={apiResponse} />}

            <Button
               type='submit'
               disabled={isLoading || !access_token || !secret_key}
               className='w-full h-11 bg-blue-600 hover:bg-blue-700 text-white font-medium'
            >
               {isLoading ? (
                  <>
                     <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                     {loadingText}
                  </>
               ) : (
                  <>{submitButtonText}</>
               )}
            </Button>
         </form>
      </div>
   );
};
