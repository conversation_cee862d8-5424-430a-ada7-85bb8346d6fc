export const types: Record<string, string> = {
   'lac-twit-usr': 'lac-twit-usr',
};

interface ChannelName {
   WOOCOMMERCE: string;
   YOUTUBE: string;
   SHOPIFY: string;
   FACEBOOK_ADS: string;
   GOOGLE_ADS: string;
   ASP: string;
   AMAZON_ADS: string;
   TWITTER: string;
   LINKEDIN: string;
   WEB: string;
   GSC: string;
   GOOGLE_ANALYTICS: string;
   META_ADS: string;
   HUBSPOT: string;
   UNICOMMERCE: string;
   ITHINKLOGISTICS: string;
}

interface FiVETRAN_CONNECTOR {
   GOOGLE_ADS: string;
   ASP: string;
   AMAZON_ADS: string;
   HUBSPOT: string;
}

export const FiVETRAN_CONNECTORS: FiVETRAN_CONNECTOR = {
   GOOGLE_ADS: 'google_ads',
   ASP: 'amazon_selling_partner',
   AMAZON_ADS: 'amazon_ads',
   HUBSPOT: 'hubspot',
};

export const channelNames: ChannelName = {
   WEB: 'flable_pixel',
   WOOCOMMERCE: 'woocommerce',
   YOUTUBE: 'youtubecomments',
   SHOPIFY: 'shopify',
   FACEBOOK_ADS: 'facebookads',
   GOOGLE_ADS: 'googleads',
   TWITTER: 'twitter',
   LINKEDIN: 'linkedin',
   ASP: FiVETRAN_CONNECTORS.ASP,
   AMAZON_ADS: FiVETRAN_CONNECTORS.AMAZON_ADS,
   GSC: 'gsc',
   GOOGLE_ANALYTICS: 'googleanalytics',
   META_ADS: 'metaads',
   HUBSPOT: 'hubspot',
   UNICOMMERCE: 'unicommerce',
   ITHINKLOGISTICS: 'ithinklogistics',
};

export const woocommerceIntegrationSteps: string[] = [
   'Please Navigate to your Store URL.',
   'Go to: WooCommerce > Settings > Advanced > REST API/ Legacy API. ( <More Info:https://woo.com/document/woocommerce-rest-api/#:~:text=Generate%20API%20keys&text=Go%20to%3A%20WooCommerce%20%3E%20Settings%20%3E,to%20the%20Key%20Details%20screen> )',
   'Select Add Key. You are taken to the Key Details screen.',
   'Add a Description.',
   'Select the User you would like to generate a key for in the dropdown ( preferably Admin User ) .',
   'Select a level of access for this API key as —  Read/Write access.',
   'Select Generate API Key, and WooCommerce creates API keys for that user.',
   'Copy the Consumer and Secret Keys and Paste them on the Fileds.',
   'Click on Connect.',
];

export const facebookAdsIntegrationSteps: string[] = [
   'In order to connect to facebook ad account, We need meta ad account id and a an access token with ads_management,ads_read,business_management, read_insights permission.',
   'Navigate to <Meta for Developers:https://www.facebook.com/login.php?next=https%3A%2F%2Fdevelopers.facebook.com%2Fapps%2F> and follow the steps provided in the <Facebook documentation:https://developers.facebook.com/docs/development/create-an-app/> to create a Facebook app.',
   'While creating the app, when you are prompted for "What do you want your app to do?", select Other. You will also need to set the app type to Business when prompted.',
   `From your App’s dashboard, <set up the Marketing API:https://developers.facebook.com/docs/marketing-apis/get-started>.`,
   'Generate a Marketing API access token: From your App’s Dashboard, click Marketing API --> Tools. Select all the available token permissions (ads_management, ads_read, read_insights, business_management) and click Get token. Copy the generated token for later use.',
   `Request a rate limit increase: Facebook heavily throttles API tokens generated from Facebook apps with the default Standard Access tier, making it infeasible to use the token for syncs with Airbyte. You'll need to request an upgrade to Advanced Access for your app on the following permissions:
Ads Management Standard Access
ads_read
Ads_management.`,
   'See the Facebook <documentation on Authorization:https://developers.facebook.com/docs/marketing-api/overview/authorization/#access-levels>',
];

const permissions = [
   'read_orders',
   'read_customers',
   'read_fulfillments',
   'read_products',
   'read_checkouts',
   'read_returns',
   'read_analytics',
   'read_customer_events',
   'read_discounts',
   'read_inventory',
   'read_marketing_events',
   'read_product_listings',
   'read_reports',
   'read_channels',
   'read_shipping',
];

export const shopifyIntegrationSteps: string[] = [
   'Please navigate to shopify admin URL',
   'Go to: Apps > Apps and sales channel Settings > Develop Apps',
   'Click on "Create an app" button',
   'Provide the app name and click on Create',
   'Go to the "API credentials Tab"',
   'Please add these following permissions to your Shopify app: ' +
      permissions.join(', '),
   'Go to the same "API credentials Tab" and click on "install app"',
   'Copy the admin access token, api key and secret and Paste them into the fields',
   'Click on connect',
];
export const unicommerceIntegrationSteps: string[] = [
   `Create API User->
Follow the guide to create a user in <User Creation Guide:http://support.unicommerce.com/index.php/knowledge-base/how-to-create-a-user/>.
After creating the user, set a password by referring to this guide:
<Password Reset Guide:http://support.unicommerce.com/index.php/knowledge-base/log-in-to-uniware-with-your-mobile-number-and-reset-password-with-otp/>`,
   `Assigning Facility Access to a User->
Navigate to Settings > Users.
Search or select the user from the list.
Click the Edit button.
Use the dropdown to search and select the required facility.
Click Save.`,
   `Facility Code Reference
Where to Find Facility Code.
Go to Settings > Facilities.
Search or select the relevant facility.
The Facility Code appears in italics before the warehouse display name in the list.`,
   `Ensure Admin Access and Facility Permissions
The API user must have the Admin role.
The user must also have access to the appropriate facility for performing API updates.
Navigate to Settings > Users.
Search or select the desired user.
Under Role, confirm the user is marked as Admin.
Click Edit to view and verify assigned facility access.`,
   `Understanding {tenant}->
The {tenant} is your Uniware account code, visible in the URL after logging in.
Example URL: https://{tenant}.unicommerce.com/`,
];
export const ithinklogisticsIntegrationSteps: string[] = [
   'Log in to your iThink Logistics dashboard.',
   'Click the Settings (⚙️) icon and navigate to the API Key section',
   'Copy both the access_token and secret_key securely — these will be used for integration.',
   'Enter your access_token and secret_key here',
   'Once submitted, the integration will automatically activate using the stored credentials',
];

export const shiprocketIntegrationSteps: string[] = [
   'Log in to your Shiprocket account.',
   `From the left-hand menu, go to Settings(⚙️):
Settings → Additional Settings → API Users → Add New API User`,
   `Click on "Create API User."`,
   `In the pop-up form:
- Enter a unique email address (must be different from the one used for your main Shiprocket login).
- Under Modules to Access, select the Shipment,Listing,Courier API Modules.`,
   `Click on "Create User."`,
   `The password for the API user will be sent to your registered email address (not the API user email ID).`,
   `Copy the password  and enter it here.`,
   `Once submitted, the integration will automatically activate using the stored credentials`,
];
