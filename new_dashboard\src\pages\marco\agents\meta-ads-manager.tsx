import { useEffect, useRef, useState } from 'react';
import React from 'react';
import { AxiosResponse } from 'axios';

import { useAppDispatch, useAppSelector } from '../../../store/store';
import usePageUnloadWarning from '../utils/meta-ads-manager-agent/external-warn';
import manualIcon from '../utils/meta-ads-manager-agent/Vector.svg';
import manualIcon2 from '../utils/meta-ads-manager-auto/Vector (2).svg';
import autoIcon from '../utils/meta-ads-manager-agent/Vector (1).svg';
import autoIcon2 from '../utils/meta-ads-manager-auto/Vector (3).svg';

import {
   setPrompt,
   setLoading,
   setCurrentChat,
   setChatLevel,
   setCampaignDetails,
   setAdsSetDetails,
   setAdsetData,
   setAdsetFinalPrompts,
   setAdCreativeData,
   setAdCreativeId,
   setIsAnyAdCreativeCreated,
   setAdsetId,
   setCampaignInsights,
   //resetMetaAdsManagerState,
} from '../../../store/reducer/meta-ads-manager-reducer';
import {
   Flex,
   IconButton,
   Stack,
   Text,
   Button,
   useToast,
   Box,
   useColorModeValue,
   Input,
   InputGroup,
   InputRightElement,
   InputLeftElement,
} from '@chakra-ui/react';
import { IoSend } from 'react-icons/io5';
import metaAdsManagerEndPoints, {
   ChatLevels,
   sendPrompt,
   SendPromptPayload,
   AdCreativePayload as BaseAdCreativePayload,
   CampaignInsights,
   ExtendedChatHistoryWithPreview,
   AnalysisDataRow,
   // CampaignDetails,
   MarcoResponse,
   ResponseCampaignData,
   ResponseAdSetData,
   ResponseAdCreativeData,
   ResponseAdsetFinal,
   targeting,
   BudgetDetails,
} from '../../../api/service/agentic-workflow/meta-ads-manager';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import {
   ADSET_PROMPTS,
   CAMPAIGN_PROMPTS,
   LOADER_MESSAGES,
} from '../utils/meta-ads-manager-agent/constants';
import { useApiMutation } from '../../../hooks/react-query-hooks';
import { v4 as uuid } from 'uuid';
import MetaAdsAgentImage from '../../../assets/image/agents/Sonny - Performance Agent.png';
import LoaderWithTextFlow from '../utils/meta-ads-manager-agent/response-loader';

const parseChatResponse = (response: string): CampaignInsights | null => {
   try {
      return JSON.parse(response) as CampaignInsights;
   } catch (error) {
      console.error('Error parsing chat response:', error);
      return null;
   }
};
export interface ExtendedAdCreativePayload extends BaseAdCreativePayload {
   analysisData: AnalysisDataRow[];
   recommendation: string;
   description?: string;
}

import { CampaignPreviewCard } from '../components/cards/camapign-previewcard';
import { AdsetPreviewCard } from '../components/cards/adset-preview';
import { AdCreativeCard } from '../components/cards/adcreative-card';
import BudgetTable from '../utils/meta-ads-manager-agent/budget-table';
import { DynamicCollapsibleTableList } from '../utils/meta-ads-manager-agent/aud-analysistab';
import { highlightCampaignDetails } from '../utils/meta-ads-manager-agent/meta-helper';
import { AdsetInsights } from '../utils/meta-ads-manager-agent/adset-insights';
import { LaunchPage } from '../utils/meta-ads-manager-agent/launch-page';
import { useNavigate } from 'react-router-dom';
//import { randomUUID } from 'node:crypto';
const MetaAdsManager = () => {
   const client_id = LocalStorageService.getItem(Keys.ClientId) as string;
   const dispatch = useAppDispatch();
   const navigate = useNavigate();
   const [isAutomationMode, setIsAutomationMode] = useState(false);

   // Handle toggle change
   // const handleModeToggle = (e: React.ChangeEvent<HTMLInputElement>) => {
   //    const isAuto = e.target.checked;
   //    setIsAutomationMode(isAuto);

   //    if (isAuto) {
   //       navigate('/marco/agents/meta-ads-manager-auto');
   //    }
   // };

   const chatEndRef = useRef<HTMLDivElement>(null);
   const inputRef = useRef<HTMLInputElement>(null);
   const {
      session_id,
      prompt,
      loading,
      currentChat,
      chatLevel,
      campaignDetails,
      AdsetDetails,
      adsetData,
      adsetFinalPrompts,
      adCreativeData,
      adCreativeCardState,
      campaignInsights,
   } = useAppSelector((state) => state.metaAdsManager);
   //const [sessionId, setSessionId] = React.useState<string>(session_id);
   const sessionId = React.useState<string>(session_id)[0];

   const isCampaignInProgress =
      Array.isArray(currentChat) && currentChat.length > 0;
   usePageUnloadWarning(isCampaignInProgress);
   console.log(setAdCreativeId), console.log(setIsAnyAdCreativeCreated);
   useEffect(() => {
      return () => {
         dispatch(setLoading(false));
      };
   }, [dispatch]);
   const handlePromptChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      dispatch(setPrompt(event.target.value));
   };
   const handleKeyDown = async (
      event: React.KeyboardEvent<HTMLInputElement>,
   ) => {
      if (event.key === 'Enter') {
         await handleSendPrompt();
      }
   };

   // const handleStartNewChat = () => {
   //    const newSessionId = Date.now().toString();
   //    setSessionId(newSessionId);
   //    dispatch(resetMetaAdsManagerState());
   // };
   const createCampaignService = useApiMutation({
      queryKey: ['create-campaign'],
      mutationFn: metaAdsManagerEndPoints.createCampaign,
   });
   const createAdsetService = useApiMutation({
      queryKey: ['create-adset'],
      mutationFn: metaAdsManagerEndPoints.createAdset,
   });
   /*const saveHistoryService = useApiMutation({
      queryKey: ['save-history'],
      mutationFn: metaAdsManagerEndPoints.saveHistory,
   });*/
   const toast = useToast();
   const handleSendPrompt = async (directPrompt?: string) => {
      if (!sessionId) return;
      const promptToSend = directPrompt || prompt;
      dispatch(setPrompt(''));
      if (promptToSend === 'Save as Draft' || promptToSend === 'Publish') {
         return;
      }
      if (!promptToSend) {
         toast({
            title: 'Please enter a prompt',
            status: 'error',
            duration: 3000,
            isClosable: true,
            position: 'bottom',
         });
         return;
      }

      const currentPrompt = promptToSend;
      setTimeout(() => {
         chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 0);
      const prevChat = currentChat;
      const chatId = uuid();
      const newMessage: ExtendedChatHistoryWithPreview = {
         id: chatId,
         question: currentPrompt,
         response: '',
         showloader: true,
      };

      const newChatList = [...(currentChat ?? []), newMessage];

      dispatch(setCurrentChat(newChatList));
      dispatch(setLoading(true));

      let payload = {
         session_id: sessionId,
         user_query: currentPrompt,
         client_id,
         chat_level: chatLevel,
      };
      if (chatLevel === 'campaign' || chatLevel === 'adset-initial') {
         payload = {
            ...payload,
            product_url: campaignDetails?.product_url,
         } as SendPromptPayload;
      }
      if (chatLevel === 'adset-final') {
         payload = {
            ...payload,
            targeting_analysis: {
               ...adsetData?.targeting_analysis,
            },
         } as SendPromptPayload;
      }
      if (chatLevel === 'ad-creative') {
         payload = {
            ...payload,
            product_url: campaignDetails?.product_url,
         } as SendPromptPayload;
      }

      try {
         const timeoutPromise = new Promise((_, reject) => {
            setTimeout(
               () =>
                  reject(
                     new Error(
                        'Request is taking longer than expected. Please wait or try again.',
                     ),
                  ),
               120000,
            );
         });
         const responsePromise = sendPrompt(payload);
         const loadingTimeout = setTimeout(() => {
            toast({
               title: 'Your request is taking longer than usual. Please wait...',
               status: 'info',
               duration: 7000,
               isClosable: true,
               position: 'bottom',
            });
         }, 60000);
         const response = (await Promise.race([
            responsePromise,
            timeoutPromise,
         ])) as { data: MarcoResponse };
         clearTimeout(loadingTimeout);
         if (!response || !response.data) {
            const updatedChat = newChatList.map((msg) => ({
               ...msg,
               showloader: false,
            }));

            dispatch(setCurrentChat(updatedChat));

            throw new Error('Cannot create Campaign please Try agian later');
         }
         switch (chatLevel) {
            case 'campaign': {
               try {
                  const response = (await Promise.race([
                     responsePromise,
                     timeoutPromise,
                  ])) as AxiosResponse<ResponseCampaignData>;

                  if (response.status === 424) {
                     const detail = (
                        response.data as unknown as {
                           detail: BudgetDetails | string;
                        }
                     ).detail;

                     dispatch(setLoading(false));
                     setTimeout(() => {
                        chatEndRef.current?.scrollIntoView({
                           behavior: 'smooth',
                        });
                     }, 0);
                     if (typeof detail === 'string') {
                        const updatedChats = newChatList.map((msg) =>
                           msg.id === chatId
                              ? { ...msg, response: detail, showloader: false }
                              : msg,
                        );
                        dispatch(setCurrentChat(updatedChats));

                        return;
                     }
                     console.log('uu', detail.budget_analysis);
                     const slicedBudgets = (detail?.budget_analysis ?? [])
                        .filter((item) => item.budget_cluster)
                        .map((item) => {
                           const [min, max] = item.budget_cluster
                              .replace(/₹/g, '')
                              .split('-')
                              .map((str) => parseInt(str, 10));

                           return `₹${Math.round((min + max) / 2)}`;
                        });

                     slicedBudgets.push('Enter Custom Budget');

                     const updatedChats = newChatList.map((msg) =>
                        msg.id === chatId
                           ? {
                                ...msg,
                                response: detail.message,
                                hasBudgetAnalysis: true,
                                budgetDetails: detail,
                                budgetOptions: slicedBudgets,
                                showBudopt: true,
                                showloader: false,
                             }
                           : msg,
                     );

                     dispatch(setCurrentChat(updatedChats));
                     return;
                  } else {
                     const {
                        name,
                        objective,
                        product_url,
                        //chat_name,
                        //chat_response,
                        daily_budget,
                        buying_type,
                        campaign_budget_optimization,
                     } = response.data;

                     let campaign;
                     let retryCount = 0;
                     const maxRetries = 3;

                     while (retryCount < maxRetries) {
                        try {
                           campaign = await createCampaignService.mutateAsync({
                              client_id,
                              name: name,
                              objective:
                                 objective === 'LINK_CLICKS'
                                    ? 'OUTCOME_TRAFFIC'
                                    : objective,
                              status: 'PAUSED',
                              special_ad_categories: ['NONE'],
                              buying_type: buying_type || 'AUCTION',
                              campaign_budget_optimization:
                                 campaign_budget_optimization || true,
                              daily_budget: Number(daily_budget) * 100,
                           });

                           if (campaign?.id) break;
                        } catch (error) {
                           const updatedChat = newChatList.map((msg) => ({
                              ...msg,
                              showloader: false,
                           }));
                           dispatch(setCurrentChat(updatedChat));
                           console.error(
                              `Campaign creation attempt ${retryCount + 1} failed:`,
                              error,
                           );
                           retryCount++;
                           if (retryCount === maxRetries) throw error;
                           await new Promise((resolve) =>
                              setTimeout(resolve, 2000),
                           );
                        }
                     }
                     if (!campaign?.id) {
                        throw new Error(
                           'Failed to create campaign after multiple attempts',
                        );
                     }
                     const campaign_details = {
                        name,
                        campaign_id: campaign.id,
                        objective,
                        product_url: product_url,
                        buying_type: buying_type || 'AUCTION',
                        campaign_budget_optimization:
                           campaign_budget_optimization || true,
                        daily_budget: daily_budget,
                     };

                     /*await saveHistoryService.mutateAsync({
                        chat_name: chat_name || 'New Campaign',
                        chat_history: JSON.stringify([
                           {
                              question: currentPrompt,
                              response: `${chat_response ?? 'Campaign created successfully'} 🚀🎉`,
                              campaignPreview: campaign_details,
                           },
                        ]),
                        chat_level: chatLevel,
                        campaign_details: JSON.stringify(
                           campaign_details as CampaignDetails,
                        ),
                        adset_details: null,
                        ad_details: null,
                        ad_creative_details: null,
                     });*/
                     dispatch(setCampaignDetails(campaign_details));
                     dispatch(setLoading(false));
                     setTimeout(() => {
                        chatEndRef.current?.scrollIntoView({
                           behavior: 'smooth',
                        });
                     }, 0);

                     const updatedChatList = newChatList.map((msg) => {
                        if (msg.showBudopt) {
                           return {
                              ...msg,
                              showBudopt: false,
                           };
                        }

                        if (msg.id === chatId) {
                           return {
                              ...msg,
                              response: 'Campaign created successfully 🚀🎉',
                              hasTables: false,
                              campaignPreview: campaign_details,
                              showBudopt: false,
                              showloader: false,
                           };
                        }
                        return msg;
                     });

                     dispatch(setCurrentChat(updatedChatList));

                     dispatch(setPrompt(''));
                     dispatch(setChatLevel('adset-initial'));
                     toast({
                        title: 'Success',
                        description: 'Campaign created successfully 🚀🎉',
                        status: 'success',
                        duration: 3000,
                        isClosable: true,
                        position: 'bottom',
                     });
                  }
               } catch (error) {
                  console.error('Campaign creation error:', error);
                  toast({
                     title: 'Error',
                     description:
                        error instanceof Error
                           ? error.message
                           : 'Failed to create campaign. Please try again with a clear campaign name and objective.',
                     status: 'error',
                     duration: 5000,
                     isClosable: true,
                  });
                  dispatch(setLoading(false));
               }
               break;
            }
            case 'adset-initial': {
               const response = (await Promise.race([
                  responsePromise,
                  timeoutPromise,
               ])) as AxiosResponse<ResponseAdSetData>;
               if (response.status === 424) {
                  const missing =
                     typeof response.data === 'object' &&
                     response.data !== null &&
                     'detail' in response.data
                        ? (response.data as { detail: string }).detail
                        : '';
                  dispatch(setLoading(false));
                  dispatch(
                     setCurrentChat([
                        ...(prevChat || []),
                        {
                           question: currentPrompt,
                           response: missing,
                        } as ExtendedChatHistoryWithPreview,
                     ]),
                  );
                  return;
               }
               const {
                  adset_name,
                  //daily_budget,
                  bid_amount,
                  targeting_analysis,
                  options,
                  chat_response,
                  // bid_strategy,
                  billing_event,
                  optimization_goal,
                  promoted_object,
               } = response.data;
               if (!targeting_analysis) {
                  throw new Error('Missing targeting analysis data');
               }
               if (chat_response) {
                  const parsedInsights = parseChatResponse(
                     JSON.stringify(chat_response),
                  );
                  if (parsedInsights) {
                     dispatch(setCampaignInsights(parsedInsights));
                  }
               }

               dispatch(
                  setAdsetData({
                     adset_name: adset_name,
                     bid_amount: bid_amount || 500,
                     bid_strategy: campaignDetails?.campaign_budget_optimization
                        ? 'LOWEST_COST_WITH_BID_CAP'
                        : 'LOWEST_COST_WITHOUT_CAP',
                     billing_event: billing_event,
                     optimization_goal: optimization_goal,
                     targeting_analysis: targeting_analysis,
                     ...(promoted_object && { promoted_object }),
                  }),
               );
               dispatch(setAdsetFinalPrompts((options as string[]) ?? []));
               /*  await saveHistoryService.mutateAsync({
                  chat_name: 'Ad Set Analysis',
                  chat_history: JSON.stringify([
                     ...(prevChat || []),
                     {
                        question: currentPrompt,
                        response: chat_response || 'Ad set analysis completed',
                        hasTables: true,
                        //tableData,
                        campaignInsights: chat_response || undefined,
                     },
                  ]),
                  chat_level: chatLevel,
                  campaign_details: JSON.stringify(campaignDetails),
                  adset_details: JSON.stringify({
                     adset_name,
                     daily_budget,
                     targeting_analysis,
                     bid_strategy,
                     billing_event,
                     optimization_goal,
                  }),
                  ad_details: null,
                  ad_creative_details: null,
               });*/

               dispatch(setLoading(false));
               setTimeout(() => {
                  chatEndRef.current?.scrollIntoView({
                     behavior: 'smooth',
                  });
               }, 0);
               dispatch(
                  setCurrentChat(
                     (prev: ExtendedChatHistoryWithPreview[] | null) => {
                        const messages = prev ? [...prev] : [];
                        messages[messages.length - 1] = {
                           question: currentPrompt,
                           response:
                              typeof chat_response === 'object' &&
                              'message' in chat_response
                                 ? String(chat_response.message)
                                 : 'Adset analysis completed',
                           hasTables: true,
                           campaignInsights: campaignInsights || undefined,
                        } as ExtendedChatHistoryWithPreview;
                        return messages;
                     },
                  ),
               );
               dispatch(setPrompt(''));
               dispatch(setChatLevel('adset-final'));
               break;
            }
            case 'adset-final': {
               const response = (await Promise.race([
                  responsePromise,
                  timeoutPromise,
               ])) as { data: ResponseAdsetFinal };
               const { chat_response, targeting } = response.data;
               const updatedTargeting = JSON.parse(
                  JSON.stringify(targeting),
               ) as targeting;
               updatedTargeting.publisher_platforms =
                  updatedTargeting.publisher_platforms?.map((p) =>
                     p.toLowerCase(),
                  ) || [];
               updatedTargeting.facebook_positions =
                  updatedTargeting.facebook_positions?.map((p) =>
                     p.toLowerCase(),
                  ) || [];
               updatedTargeting.instagram_positions =
                  updatedTargeting.instagram_positions?.map((p) =>
                     p.toLowerCase(),
                  ) || [];

               updatedTargeting.instagram_positions = [
                  'stream',
                  'explore',
                  'reels',
               ];
               //updatedTargeting.instagram_positions.push('stream');
               if (!campaignDetails?.campaign_id) {
                  throw new Error('Campaign ID is required to create an adset');
               }
               if (!adsetData) {
                  throw new Error('Adset data is required');
               }

               try {
                  const adsetResponse = await createAdsetService.mutateAsync({
                     client_id,
                     campaign_id: campaignDetails.campaign_id,
                     name: adsetData.adset_name || 'Default Adset Name',
                     bid_amount: adsetData.bid_amount || 500,
                     bid_strategy:
                        adsetData.bid_strategy || 'LOWEST_COST_WITH_BID_CAP',
                     billing_event: adsetData.billing_event || 'IMPRESSIONS',
                     optimization_goal:
                        adsetData.optimization_goal || 'LINK_CLICKS',
                     ...(adsetData.optimization_goal ===
                        'OFFSITE_CONVERSIONS' && adsetData.promoted_object
                        ? { promoted_object: adsetData.promoted_object }
                        : {}),
                     targeting: updatedTargeting,
                     status: 'PAUSED',
                  });
                  if (!adsetResponse?.id) {
                     throw new Error('Failed to get adset ID from response');
                  }
                  const Adset_Details = {
                     adset_id: adsetResponse.id,
                     adset_name: adsetData.adset_name,
                     bid_amount: adsetData.bid_amount,
                     targeting_analysis: updatedTargeting,
                     options: {},
                     chat_response: {},
                     bid_strategy: adsetData.bid_strategy || '',
                     billing_event: adsetData.billing_event || '',
                     optimization_goal: adsetData.optimization_goal || '',
                     promoted_object: adsetData.promoted_object,
                  };
                  //const validated_budget = daily_budget_paise;
                  dispatch(
                     setAdsetData({
                        ...adsetData,
                     }),
                  );

                  dispatch(setAdsSetDetails(Adset_Details));
                  console.log(AdsetDetails);
                  dispatch(setLoading(false));
                  setTimeout(() => {
                     chatEndRef.current?.scrollIntoView({
                        behavior: 'smooth',
                     });
                  }, 0);
                  const actions = [
                     setAdsetId(adsetResponse.id),
                     setCurrentChat([
                        ...(prevChat || []),
                        {
                           question: currentPrompt,
                           response: `${chat_response ?? 'AdSet created successfully'} ✅`,
                           hasTables: false,
                           AdsetPreview: Adset_Details,
                        } as ExtendedChatHistoryWithPreview,
                     ]),

                     setChatLevel('ad-creative' as ChatLevels),
                     setPrompt(''),
                  ];

                  actions.forEach((action) => dispatch(action));
                  /* await saveHistoryService.mutateAsync({
                     chat_name: 'Ad Set Creation',
                     chat_history: JSON.stringify([
                        ...(prevChat || []),
                        {
                           question: currentPrompt,
                           response:
                              chat_response ?? 'AdSet created successfully',
                           hasTables: false,
                        },
                     ]),
                     chat_level: chatLevel,
                     campaign_details: JSON.stringify(campaignDetails),
                     adset_details: JSON.stringify({
                        ...adsetData,
                     }),
                     ad_details: null,
                     ad_creative_details: null,
                  });*/
                  toast({
                     title: 'Success',
                     description: 'AdSet created successfully 🚀🎉 ',
                     status: 'success',
                     duration: 5000,
                     isClosable: true,
                     position: 'bottom',
                  });
               } catch (error) {
                  const updatedChat = newChatList.map((msg) => ({
                     ...msg,
                     showloader: false,
                  }));
                  dispatch(setCurrentChat(updatedChat));
                  console.error('Adset creation failed:', error);
                  toast({
                     title: 'Error',
                     description:
                        error instanceof Error
                           ? error.message
                           : 'Failed to create ad set. Please try again.',
                     status: 'error',
                     duration: 5000,
                     isClosable: true,
                     position: 'top',
                  });
                  dispatch(setLoading(false));
               }
               break;
            }
            case 'ad-creative': {
               const response = (await Promise.race([
                  responsePromise,
                  timeoutPromise,
               ])) as { data: ResponseAdCreativeData };
               const {
                  ad_creative_name,
                  caption,
                  image_url,
                  description,
                  ad_creative_analysis,
                  recommendation,
               } = response.data;
               const chat_response = 'Ad creative preview generated';
               const creative_name: string = ad_creative_name;
               const description_text: string = description;
               const pageIdResponse = await metaAdsManagerEndPoints.fetchPageId(
                  {
                     client_id: client_id,
                  },
               );
               const page_id = pageIdResponse.data;
               const product_url =
                  campaignDetails?.product_url || 'https://develove.flable.ai/';
               const object_story_spec = {
                  page_id: page_id,
                  link_data: {
                     link: product_url,
                     message: caption,
                     name: creative_name,
                     description: description_text,
                     picture: image_url,
                     call_to_action: {
                        type: 'SHOP_NOW',
                        value: {
                           link: product_url,
                        },
                     },
                  },
               };
               const analysisTableData: AnalysisDataRow[] = (
                  Array.isArray(ad_creative_analysis)
                     ? ad_creative_analysis
                     : []
               ).map((item: { [key: string]: { [key: string]: number } }) => {
                  const [objectType, data] = Object.entries(item)[0];
                  return {
                     objective_type: objectType,
                     spend: data?.spend ?? 0,
                     roas: data?.roas ?? 0,
                     cpp: data?.cpp ?? 0,
                     ctr: data?.ctr ?? 0,
                     cpc: data?.cpc ?? 0,
                     purchase: data?.purchase ?? 0,
                  };
               });
               const adCreativePayload: ExtendedAdCreativePayload = {
                  client_id,
                  name: creative_name || `Ad_${Date.now()}`,
                  object_story_spec,
                  analysisData: analysisTableData,
                  recommendation:
                     typeof recommendation === 'string' ? recommendation : '',
                  description:
                     typeof description === 'string' ? description : undefined,
               };
               dispatch(setAdCreativeData(adCreativePayload));
               const updatedMessage = {
                  question: currentPrompt,
                  response: chat_response,
                  hasTables: false,
                  adCreativeData: adCreativePayload,
               } as ExtendedChatHistoryWithPreview;
               /*   await saveHistoryService.mutateAsync({
                  chat_name: 'Ad Creative Preview',
                  chat_history: JSON.stringify([
                     ...(prevChat || []),
                     updatedMessage,
                  ]),
                  chat_level: chatLevel,
                  campaign_details: JSON.stringify(campaignDetails),
                  adset_details: JSON.stringify(adsetData),
                  ad_details: null,
                  ad_creative_details: JSON.stringify(adCreativePayload),
               });*/
               dispatch(setLoading(false));
               setTimeout(() => {
                  chatEndRef.current?.scrollIntoView({
                     behavior: 'smooth',
                  });
               }, 0);

               dispatch(
                  setCurrentChat(
                     (prev: ExtendedChatHistoryWithPreview[] | null) => {
                        const messages = prev ? [...prev] : [];

                        messages[messages.length - 1] = updatedMessage;

                        return messages;
                     },
                  ),
               );
               dispatch(setPrompt(''));
               //dispatch(setChatLevel('completed'))
               break;
            }
         }
      } catch (error) {
         console.error('Error in handleSendPrompt:', error);
         if (
            error instanceof Error &&
            error.message.includes('Request is taking longer than expected')
         ) {
            toast({
               title: 'Request Timeout',
               description:
                  'The request is taking too long. Please try again or check your connection.',
               status: 'error',
               duration: 7000,
               isClosable: true,
               position: 'bottom',
            });
         } else {
            toast({
               title: 'Error',
               description:
                  error instanceof Error
                     ? error.message
                     : 'Failed to process request. Please try again.',
               status: 'error',
               duration: 5000,
               isClosable: true,
               position: 'bottom',
            });
         }
         dispatch(setLoading(false));
      }
   };

   const isAdCreated = Boolean(
      adCreativeCardState.adCreativeId || adCreativeCardState.adId,
   );
   return (
      <Flex
         sx={{ width: '100%', height: '100%' }}
         direction='row'
         alignItems='flex-start'
         boxShadow='0 0 4px rgba(0, 0, 0, 0.1)'
      >
         <Flex
            sx={{ width: '100%', height: '100%' }}
            flexDirection='column'
            justifyContent='space-between'
            alignItems='center'
         >
            <Flex
               mt={10}
               width='100%'
               flexDirection='column'
               alignItems='flex-start'
               overflowY='auto'
               maxW='900px'
            >
               <Flex width='100%' justifyContent='center' mb={4}>
                  <div className='flex bg-[#E4F2FF] rounded-[16px] p-1 w-fit shadow-sm border border-[#E0E7EF]'>
                     <button
                        className={`flex items-center gap-2 px-6 py-2 rounded-[12px] text-base font-medium transition-colors duration-150 focus:outline-none ${!isAutomationMode ? 'bg-white text-[#3C76E1] border border-blue-500' : 'text-[#6B7280]'} cursor-pointer`}
                        style={{
                           boxShadow: !isAutomationMode
                              ? '0 0 0 1px #3C76E1'
                              : 'none',
                        }}
                        onClick={() => {
                           setIsAutomationMode(false);
                           navigate('/marco/meta-ads-manager-agent');
                        }}
                        type='button'
                     >
                        <img
                           src={!isAutomationMode ? manualIcon2 : manualIcon}
                           alt='Manual'
                           className='w-5 h-5'
                        />
                        Interactive Mode
                     </button>
                     <button
                        className={`flex items-center gap-2 px-6 py-2 rounded-[12px]  text-base font-medium transition-colors duration-150 focus:outline-none ml-1 ${isAutomationMode ? 'bg-white text-[#2563EB] shadow border border-[#2563EB]' : 'text-[#333333]'} cursor-pointer`}
                        onClick={() => {
                           setIsAutomationMode(true);
                           navigate('/marco/meta-ads-manager-auto');
                        }}
                        type='button'
                     >
                        <img src={autoIcon} alt='Auto' className='w-5 h-5' />
                        AutoPilot
                     </button>
                  </div>
               </Flex>

               <Flex width='100%' justifyContent='center' mb={4}>
                  <div className='w-40 h-40 overflow-hidden'>
                     <img
                        src={MetaAdsAgentImage}
                        alt='cropped'
                        className='w-36'
                     />
                  </div>
               </Flex>
               <Flex width='100%' justifyContent='center' mb={4}>
                  <p className='w-full  text-center font-bold text-[16px] md:text-[20px] text-black '>
                     Meta Ads Manager Agent
                  </p>
               </Flex>

               <Flex width='100%' justifyContent='center' mb={4}>
                  {!currentChat && (
                     <Text
                        width='50%'
                        textAlign='center'
                        fontSize={14}
                        size='lg'
                        fontWeight={400}
                        color='black'
                        padding='10px'
                        onClick={() => setPrompt('')}
                        maxWidth='447px'
                     >
                        Hello! Welcome to your Meta Ads Manager Agent. I'm here
                        to help you create, manage, and optimize your ad
                        campaigns effortlessly. Try these prompts to get
                        started:
                     </Text>
                  )}
               </Flex>
               <Stack
                  width='100%'
                  mt='2'
                  spacing='2'
                  direction='row'
                  justifyContent='center'
                  px={4}
                  mb='2'
               >
                  {!currentChat &&
                     CAMPAIGN_PROMPTS.map((prompt: string, index: number) => (
                        <Box
                           key={index}
                           border='1px'
                           borderColor='gray.300'
                           padding='5px 15px'
                           borderRadius='15px'
                           width='180px'
                           cursor='pointer'
                           boxShadow='md'
                           fontWeight={500}
                           lineHeight='1.4'
                           sx={{
                              overflow: 'auto',
                              wordWrap: 'break-word',
                              whiteSpace: 'normal',
                           }}
                           bg={useColorModeValue('white', 'var(--controls)')}
                           _hover={{
                              bg: useColorModeValue(
                                 'gray.100',
                                 'var(--controls-hover)',
                              ),
                              color: useColorModeValue('black', 'white'),
                           }}
                           onClick={() => {
                              dispatch(setPrompt(prompt));
                              inputRef.current?.focus();
                           }}
                        >
                           {highlightCampaignDetails(prompt)}
                        </Box>
                     ))}
               </Stack>
               {currentChat &&
                  currentChat.map(
                     (item: ExtendedChatHistoryWithPreview, index: number) => (
                        <React.Fragment key={index}>
                           {item.question && (
                              <Flex
                                 width='100%'
                                 direction='row'
                                 justifyContent='flex-end'
                                 padding='10px'
                                 px={4}
                                 mt={3}
                              >
                                 <Text
                                    fontSize='14px'
                                    bg='#3366FF'
                                    color='white'
                                    px={6}
                                    py={3}
                                    borderRadius='md'
                                    fontWeight='500'
                                    textAlign='center'
                                    display='inline-block'
                                    maxW='fit-content'
                                 >
                                    {item.question}
                                 </Text>
                              </Flex>
                           )}
                           {item.showloader && !item.response ? (
                              <Flex
                                 width='100%'
                                 direction='row'
                                 justifyContent='flex-start'
                                 alignItems='center'
                                 padding='10px'
                                 px={4}
                              >
                                 <LoaderWithTextFlow
                                    messages={
                                       LOADER_MESSAGES[
                                          chatLevel as keyof typeof LOADER_MESSAGES
                                       ]
                                    }
                                 />
                              </Flex>
                           ) : (
                              <Flex
                                 width='100%'
                                 direction='column'
                                 justifyContent='flex-start'
                                 alignItems='flex-start'
                                 padding='10px'
                                 px={4}
                              >
                                 {chatLevel === 'ad-creative' &&
                                 item.adCreativeData ? (
                                    <Flex
                                       width='100%'
                                       justifyContent='flex-start'
                                       mt={4}
                                       flexDirection='column'
                                    >
                                       <AdCreativeCard messageIndex={index} />
                                       {isAdCreated && (
                                          <LaunchPage messageIndex={index} />
                                       )}
                                    </Flex>
                                 ) : (
                                    <>
                                       {item.response && (
                                          <Flex
                                             direction='row'
                                             alignItems='center'
                                             width='100%'
                                             mt={4}
                                          >
                                             <Text
                                                fontSize='14px'
                                                bg='#F8F8F8'
                                                color='black'
                                                px={6}
                                                py={3}
                                                borderRadius='md'
                                                fontWeight='500'
                                                textAlign='center'
                                                display='inline-block'
                                                maxW='fit-content'
                                                ml={2.5}
                                             >
                                                {item.response}
                                             </Text>
                                          </Flex>
                                       )}

                                       {item.hasTables &&
                                          adsetData?.targeting_analysis && (
                                             <Flex
                                                width='100%'
                                                justifyContent='flex-start'
                                                flexDirection='column'
                                                mt={4}
                                             >
                                                <AdsetInsights />
                                                <Text
                                                   fontSize='lg'
                                                   fontWeight='bold'
                                                   mb={6}
                                                >
                                                   Audience Analysis
                                                </Text>
                                                <DynamicCollapsibleTableList
                                                   data={
                                                      adsetData?.targeting_analysis || {
                                                         geo_locations: {
                                                            countries: [],
                                                         },
                                                         age: [],
                                                         audience_interests: [],
                                                         Placement: [],
                                                         audience_behaviors: [],
                                                         region: [],
                                                      }
                                                   }
                                                />
                                                {/* <TablesComponent/>*/}
                                             </Flex>
                                          )}
                                       {item.hasBudgetAnalysis &&
                                          item.budgetDetails && (
                                             <Flex
                                                width='100%'
                                                justifyContent='flex-start'
                                                mt={4}
                                             >
                                                <BudgetTable
                                                   data={
                                                      item.budgetDetails
                                                         .budget_analysis
                                                   }
                                                   recommendation={
                                                      item.budgetDetails
                                                         .recommendation
                                                   }
                                                />
                                             </Flex>
                                          )}
                                       {item.showBudopt &&
                                          item.budgetOptions && (
                                             <Box
                                                width='100%'
                                                display='flex'
                                                justifyContent='center'
                                             >
                                                <Text
                                                   mt={6}
                                                   mr={8}
                                                   fontWeight='md'
                                                   color='blue.500'
                                                >
                                                   Reccomended Budgets :
                                                </Text>
                                                <Flex
                                                   gap={4}
                                                   justify='center'
                                                   align='center'
                                                   mt={4}
                                                   wrap='wrap'
                                                >
                                                   {item.budgetOptions.map(
                                                      (option) => (
                                                         <Button
                                                            key={option}
                                                            variant='outline'
                                                            isDisabled={loading}
                                                            onClick={() => {
                                                               if (
                                                                  option ===
                                                                  'Enter Custom Budget'
                                                               ) {
                                                                  inputRef.current?.focus(); // Focus the existing input
                                                               } else {
                                                                  void handleSendPrompt(
                                                                     `Set budget to ${option}`,
                                                                  );
                                                               }
                                                            }}
                                                            borderColor='blue.500'
                                                            color='blue.500'
                                                            _hover={{
                                                               bg: 'blue.500',
                                                               color: 'white',
                                                               borderColor:
                                                                  'blue.500',
                                                            }}
                                                            borderRadius='full'
                                                            px={6}
                                                            py={4}
                                                            fontWeight='500'
                                                            fontSize='md'
                                                         >
                                                            {option}
                                                         </Button>
                                                      ),
                                                   )}
                                                </Flex>
                                             </Box>
                                          )}
                                       {item.campaignPreview && (
                                          <Flex
                                             width='100%'
                                             justifyContent='flex-start'
                                             mt={4}
                                          >
                                             <CampaignPreviewCard
                                                campaignDetails={
                                                   item.campaignPreview
                                                }
                                             />
                                          </Flex>
                                       )}
                                       {item.AdsetPreview && (
                                          <Flex
                                             width='100%'
                                             justifyContent='flex-start'
                                             mt={4}
                                          >
                                             <AdsetPreviewCard
                                                AdsetDetails={item.AdsetPreview}
                                             />
                                          </Flex>
                                       )}
                                    </>
                                 )}
                              </Flex>
                           )}
                        </React.Fragment>
                     ),
                  )}
               <div ref={chatEndRef}></div>
               {!loading &&
                  chatLevel === 'adset-initial' &&
                  !currentChat?.some((msg) => msg.adCreativeData) && (
                     <Stack
                        width='100%'
                        mt='3'
                        spacing='3'
                        direction='row'
                        justifyContent='center'
                        px={4}
                     >
                        {ADSET_PROMPTS.map((prompt: string, index: number) => (
                           <Text
                              key={index}
                              fontSize={14}
                              width='35%'
                              background='gray.100'
                              padding={3}
                              borderRadius={5}
                              textAlign='center'
                              sx={{
                                 cursor: 'pointer',
                                 '&:hover': {
                                    background: 'gray.200',
                                    transform: 'translateY(-2px)',
                                    boxShadow: 'md',
                                 },
                                 transition: 'all 0.2s ease-in-out',
                              }}
                              onClick={() => {
                                 dispatch(setPrompt(prompt));
                                 inputRef.current?.focus();
                              }}
                           >
                              {prompt}
                           </Text>
                        ))}
                     </Stack>
                  )}
               {!loading &&
                  chatLevel === 'adset-final' &&
                  !currentChat?.some((msg) => msg.adCreativeData) && (
                     <Stack
                        width='100%'
                        mt='3'
                        spacing='3'
                        direction='row'
                        justifyContent='center'
                        px={4}
                     >
                        {adsetFinalPrompts.map(
                           (prompt: string, index: number) => (
                              <Text
                                 key={index}
                                 fontSize={14}
                                 width='35%'
                                 background='gray.100'
                                 padding={3}
                                 borderRadius={5}
                                 textAlign='center'
                                 sx={{
                                    cursor: 'pointer',
                                    '&:hover': {
                                       background: 'gray.200',
                                       transform: 'translateY(-2px)',
                                       boxShadow: 'md',
                                    },
                                    transition: 'all 0.2s ease-in-out',
                                 }}
                                 onClick={() => {
                                    dispatch(setPrompt(prompt));
                                    inputRef.current?.focus();
                                 }}
                              >
                                 {prompt}
                              </Text>
                           ),
                        )}
                     </Stack>
                  )}
               {!loading && chatLevel === 'ad-creative' && !adCreativeData && (
                  <Flex
                     width='100%'
                     justifyContent='center'
                     alignItems='center'
                     mt='6'
                     mb='6'
                  >
                     <Button
                        variant='outline'
                        borderColor='blue.500'
                        size='sm'
                        onClick={() =>
                           void handleSendPrompt('Create an ad creative')
                        }
                        px='3'
                        py='6'
                        fontSize='md'
                        fontWeight='600'
                        _hover={{
                           transform: 'translateY(-2px)',
                           boxShadow: 'lg',
                           bg: 'blue.500',
                           color: 'white',
                        }}
                        _active={{
                           transform: 'translateY(0)',
                        }}
                        transition='all 0.2s'
                        borderRadius='md'
                        color='blue.500'
                     >
                        Proceed with Ad Creation
                     </Button>
                  </Flex>
               )}
            </Flex>
            <Flex
               flexDirection='row'
               alignItems='center'
               margin='10px'
               padding='0'
               flexWrap='nowrap'
               justifyContent='center'
               gap={3}
               bg='white'
               borderRadius='2xl'
               position='sticky'
               bottom={4}
               zIndex={1}
               width='95%'
               maxW='900px'
               mx='auto'
            >
               <InputGroup
                  height='62px'
                  borderRadius='2xl'
                  bg='white'
                  boxShadow='md'
                  border='1px solid #e5e7eb'
                  px='3'
                  py='2'
               >
                  {/* Toggle Buttons inside Input */}
                  <InputLeftElement
                     width='auto'
                     ml='6px'
                     top='50%'
                     transform='translateY(-50%)'
                  >
                     <Box className='flex items-center bg-[#EAF3FF] px-2 py-1 rounded-xl gap-1'>
                        <button
                           className={`w-10 h-10 flex items-center justify-center cursor-pointer rounded-lg transition-all duration-150 border ${
                              !isAutomationMode
                                 ? 'bg-white border-blue-500 shadow'
                                 : 'border-transparent'
                           }`}
                           style={{
                              boxShadow: !isAutomationMode
                                 ? '0 0 0 1px #3C76E1'
                                 : 'none',
                           }}
                           onClick={() => {
                              setIsAutomationMode(false);
                              navigate('/marco/meta-ads-manager-agent');
                           }}
                           title='Manual Mode'
                        >
                           <img
                              src={!isAutomationMode ? manualIcon2 : manualIcon}
                              alt='Manual Mode'
                              className='w-5 h-5'
                           />
                        </button>
                        <button
                           className={`w-10 h-10 flex items-center justify-center cursor-pointer rounded-lg transition-all duration-150 border ${
                              isAutomationMode
                                 ? 'bg-white border-blue-500 shadow'
                                 : 'border-none'
                           }`}
                           style={{
                              boxShadow: isAutomationMode
                                 ? '0 0 0 2px #2563EB'
                                 : 'none',
                           }}
                           onClick={() => {
                              setIsAutomationMode(true);
                              navigate('/marco/meta-ads-manager-auto');
                           }}
                           title='Auto Mode'
                        >
                           <img
                              src={autoIcon2}
                              alt='Auto Mode'
                              className='w-5 h-5'
                           />
                        </button>
                     </Box>
                  </InputLeftElement>

                  {/* Main Input (Prompt) */}
                  <Input
                     placeholder='Type your prompt here'
                     ref={inputRef}
                     value={prompt}
                     disabled={loading}
                     onChange={handlePromptChange}
                     onKeyDown={(e) => void handleKeyDown(e)}
                     height='52px'
                     pl='105px'
                     pr='52px'
                     pb='10px'
                     fontSize='md'
                     borderRadius='2xl'
                     border='none'
                     bg='transparent'
                     _focus={{ border: 'none', boxShadow: 'none' }}
                  />

                  {/* Send Button */}
                  <InputRightElement
                     mr='8px'
                     top='50%'
                     transform='translateY(-50%)'
                  >
                     <IconButton
                        aria-label='Send'
                        icon={<IoSend />}
                        background='#437eeb'
                        color='white'
                        borderRadius='md'
                        height='40px'
                        width='40px'
                        _hover={{ background: '#3c76e1' }}
                        onClick={() => void handleSendPrompt()}
                        isDisabled={loading || !prompt.trim()}
                     />
                  </InputRightElement>
               </InputGroup>
            </Flex>
         </Flex>
      </Flex>
   );
};
export default MetaAdsManager;
