import { Tooltip, TooltipContent, Toolt<PERSON>Provider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/utils';

export interface Tooltipprops {
   children: React.ReactNode;
   content: string;
   side?: 'top' | 'right' | 'bottom' | 'left';
   className?: string;
   asChild?: boolean;
}

function Tooltips({ children, content, side = 'top', className, asChild }: Tooltipprops) {
   return (
      <TooltipProvider>
         <Tooltip>
            <TooltipTrigger asChild={asChild}>{children}</TooltipTrigger>
            <TooltipContent
               side={side}
               className={cn(
                  'z-10 shadow-md para4 text-cerulean px-3 rounded-sm py-2 bg-white border-fog overflow-hidden animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
                  className,
               )}
            >
               <p>{content}</p>
            </TooltipContent>
         </Tooltip>
      </TooltipProvider>
   );
}

export default Tooltips;
