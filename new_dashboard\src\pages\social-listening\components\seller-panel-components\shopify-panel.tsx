import React from 'react';
import { <PERSON>ader2, <PERSON><PERSON><PERSON>cle2, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { cn } from '@/utils';

interface FormFieldProps {
   id: string;
   name: string;
   type: string;
   label: string;
   placeholder: string;
   value: string;
   onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
   required?: boolean;
   disabled?: boolean;
   className?: string;
}

interface StoreNameFieldProps extends Omit<FormFieldProps, 'type'> {
   suffix?: string;
}

interface ApiResponseProps {
   success: boolean;
   message: string;
}

interface ModernSellerPanelProps {
   title: string;
   description: string;
   storeName: string;
   apiKey: string;
   apiSecret: string;
   adminAccessToken: string;
   onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
   onSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
   isLoading: boolean;
   apiResponse: ApiResponseProps | null;
   submitButtonText?: string;
   loadingText?: string;
}

export const ModernFormField: React.FC<FormFieldProps> = ({
   id,
   name,
   type,
   label,
   placeholder,
   value,
   onChange,
   required = false,
   disabled = false,
   className,
}) => {
   return (
      <div className={cn('space-y-2', className)}>
         <Label htmlFor={id} className='text-sm font-medium text-gray-700'>
            {label}
            {required && <span className='text-red-500 ml-1'>*</span>}
         </Label>
         <Input
            id={id}
            name={name}
            type={type}
            placeholder={placeholder}
            value={value}
            onChange={onChange}
            required={required}
            disabled={disabled}
            className='h-11 border-gray-300 focus:border-blue-500 focus:outline-none focus:ring-blue-700'
         />
      </div>
   );
};

export const StoreNameField: React.FC<StoreNameFieldProps> = ({
   id,
   name,
   label,
   placeholder,
   value,
   onChange,
   required = false,
   disabled = false,
   className,
   suffix = '.myshopify.com',
}) => {
   return (
      <div className={cn('space-y-2', className)}>
         <Label htmlFor={id} className='text-sm font-medium text-gray-700'>
            {label}
            {required && <span className='text-red-500 ml-1'>*</span>}
         </Label>
         <div className='flex'>
            <Input
               id={id}
               name={name}
               type='text'
               placeholder={placeholder}
               value={value}
               onChange={onChange}
               required={required}
               disabled={disabled}
               className='h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-600 rounded-r-none border-r-0'
            />
            <div className='flex items-center px-3 h-11 bg-gray-50 border border-gray-300 border-l-0 rounded-r-md text-sm text-gray-500'>
               {suffix}
            </div>
         </div>
      </div>
   );
};

export const ModernApiResponse: React.FC<{ response: ApiResponseProps }> = ({
   response,
}) => {
   if (!response.message) return null;

   return (
      <Alert
         className={cn(
            'border-l-4',
            response.success
               ? 'border-l-green-500 bg-green-50 text-green-800'
               : 'border-l-red-500 bg-red-50 text-red-800',
         )}
      >
         {response.success ? (
            <CheckCircle2 className='h-4 w-4' />
         ) : (
            <AlertCircle className='h-4 w-4' />
         )}
         <AlertDescription className='font-medium'>
            {response.message}
         </AlertDescription>
      </Alert>
   );
};

export const ShopifySellerPanel: React.FC<ModernSellerPanelProps> = ({
   title,
   description,
   storeName,
   apiKey,
   apiSecret,
   adminAccessToken,
   onChange,
   onSubmit,
   isLoading,
   apiResponse,
   submitButtonText = 'Connects',
   loadingText = 'Connecting...',
}) => {
   return (
      <div className='space-y-6'>
         <div className='space-y-2'>
            <h3 className='text-lg font-semibold text-gray-900'>{title}</h3>
            <p className='text-sm text-gray-600'>{description}</p>
         </div>

         <form onSubmit={onSubmit} className='space-y-6'>
            <div className='space-y-3'>
               <StoreNameField
                  id='text'
                  name='storeName'
                  label='Store Name'
                  placeholder='Enter your store name...'
                  value={storeName}
                  onChange={onChange}
                  required
                  disabled={isLoading}
               />

               <ModernFormField
                  id='api-key'
                  name='apiKey'
                  label=' API Key'
                  placeholder='eg. xxxxxxxx'
                  value={apiKey}
                  onChange={onChange}
                  type='password'
                  required
                  disabled={isLoading}
               />
               <ModernFormField
                  id='api-secret'
                  name='apiSecret'
                  label='API Secret '
                  placeholder='eg. xxxxxxxx'
                  value={apiSecret}
                  onChange={onChange}
                  type='password'
                  required
                  disabled={isLoading}
               />
               <ModernFormField
                  id='admin-access-token'
                  name='adminAccessToken'
                  label='Admin Access Token '
                  placeholder='eg. xxxxxxxx'
                  value={adminAccessToken}
                  onChange={onChange}
                  type='password'
                  required
                  disabled={isLoading}
               />
            </div>

            {apiResponse && <ModernApiResponse response={apiResponse} />}

            <Button
               type='submit'
               disabled={
                  isLoading ||
                  !storeName ||
                  !apiKey ||
                  !apiSecret ||
                  !adminAccessToken
               }
               className='w-full h-11 bg-blue-600 hover:bg-blue-700 text-white font-medium'
            >
               {isLoading ? (
                  <>
                     <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                     {loadingText}
                  </>
               ) : (
                  <>{submitButtonText}</>
               )}
            </Button>
         </form>
      </div>
   );
};
