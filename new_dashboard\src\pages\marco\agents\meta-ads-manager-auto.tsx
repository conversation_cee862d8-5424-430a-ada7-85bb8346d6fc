import { useState, useEffect, useRef } from 'react';
import React from 'react';
import { useNavigate } from 'react-router-dom';
import manualIcon from '../utils/meta-ads-manager-agent/Vector.svg';
import manualIcon2 from '../utils/meta-ads-manager-auto/Vector (2).svg';
import autoIcon from '../utils/meta-ads-manager-agent/Vector (1).svg';
import autoIcon2 from '../utils/meta-ads-manager-auto/Vector (3).svg';
import { useToast } from '@chakra-ui/react';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
   HoverCard,
   HoverCardContent,
   HoverCardTrigger,
} from '@/components/ui/hover-card';
import { cn } from '@/utils';
import {
   extractTargetingTables,
   TableSection,
} from '../utils/meta-ads-manager-auto/extractTargetTable';
import {
   //defaultTabListClass,
   defaultTabTriggerClass,
} from '@/constants/defaultClasses';
import { HoverCardArrow } from '@radix-ui/react-hover-card';
import { Separator } from '@/components/ui/separator';
import Tooltips from '@/components/tooltip';
import { IoSend } from 'react-icons/io5';
import { BsExclamationTriangleFill } from 'react-icons/bs';
import { copilot_prompts } from '../utils/meta-ads-manager-auto/constants';
import metaAdsManagerEndPoints, {
   sendPrompt,
   SendPromptPayload,
   CampaignDetails,
   ResponseCampaignData,
   ResponseAdCreativeData,
   ResponseAdsetFinal,
   targeting,
   AdsetDetails,
   AdsetData,
   ResponseAdSetData,
   CreateAdPayload,
} from '../../../api/service/agentic-workflow/meta-ads-manager';
import { LocalStorageService, Keys } from '../../../utils/local-storage';
import { AuthUser } from '../../../types/auth';
import { useApiMutation } from '../../../hooks/react-query-hooks';
import MetaAdsAgentImage from '../../../assets/image/agents/Sonny - Performance Agent.png';
import SummaryAgentMessage from '../utils/meta-ads-manager-auto/summary-message';
import StreamingMessageTimeline from '../utils/meta-ads-manager-auto/StreamingMessageTimeline';
import { useAppDispatch, useAppSelector } from '../../../store/store';
import {
   setUserPrompt,
   setIsCreating,
   setCurrentStep,
   addStreamingMessage,
   updateStreamingMessage,
   updateStepStatus,
   setCampaignDetails,
   setAdsetDetails,
   setAdsetData,
   setAdCreativeId,
   setAdId,
   setSummaryLoading,
   setSummaryContent,
   setStreamingMessages,
   setStepStatuses,
} from '../../../store/reducer/metaAdsAutoAgentReducer';
import { clearAnimationCache } from '../utils/meta-ads-manager-auto/type-write-text';
import {
   useFetchMetaAdsAutoAgentHistoryBySessionQuery,
   useSaveMetaAdsAutoAgentHistoryMutation,
} from '../apis/meta-ads-auto-agent-apis';
import { fetchMetaCredentials } from '../utils/meta-ads-manager-auto/fetchCred';
import CampaignSummaryCard from '../utils/meta-ads-manager-auto/campaign-summary-card';
import { Textarea } from '../utils/meta-ads-manager-auto/text-area';
import { Button } from '@/components/ui/button';
//import CreativePreview from '../utils/meta-ads-manager-auto/creative-preview';
//import { uploadImageUrlToAzure } from '../../../utils/imageUpload';

const MetaAdsManagerAuto = () => {
   const toast = useToast();
   const navigate = useNavigate();
   const dispatch = useAppDispatch();

   const reduxState = useAppSelector((state) => state.metaAdsAutoAgent);

   const latestStateRef = useRef(reduxState);
   useEffect(() => {
      latestStateRef.current = reduxState;
   }, [reduxState]);

   const {
      userPrompt,
      isCreating,
      currentStep,
      sessionId,
      streamingMessages,
      stepStatuses,
      summaryLoading,
      summaryContent,
   } = useAppSelector((state) => state.metaAdsAutoAgent);

   useEffect(() => {
      const style = document.createElement('style');
      style.textContent = `
         /* Hide scrollbars for webkit browsers */
         ::-webkit-scrollbar {
            display: none !important;
            width: 0px !important;
            background: transparent !important;
         }
         ::-webkit-scrollbar-track {
            display: none !important;
         }
         ::-webkit-scrollbar-thumb {
            display: none !important;
         }
         /* Hide scrollbars for Firefox */
         * {
            scrollbar-width: none !important;
            -ms-overflow-style: none !important;
         }
         /* Ensure body doesn't show scrollbars */
         body {
            overflow-x: hidden !important;
         }
      `;
      document.head.appendChild(style);

      return () => {
         if (document.head.contains(style)) {
            document.head.removeChild(style);
         }
      };
   }, []);

   const scrollRef = useRef<HTMLDivElement>(null);
   const [Metacredentials, setMetaCredentials] = useState<boolean | null>(null);
   const [isAutomationMode, setIsAutomationMode] = useState(true);

   const [activeStreamingIndex, setActiveStreamingIndex] = useState(0);

   const [isHistorySession, setIsHistorySession] = useState(false);
   const [flowError, setFlowError] = useState(false);
   const [lastUserQuestion, setLastUserQuestion] = useState('');
   const [showSkeletonLoader, setShowSkeletonLoader] = useState(false);

   const chatEndRef = useRef<HTMLDivElement>(null);

   const userDetails = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   );
   const client_id = userDetails?.client_id || '';

   const createCampaignService = useApiMutation({
      queryKey: ['create-campaign'],
      mutationFn: metaAdsManagerEndPoints.createCampaign,
   });
   const createAdsetService = useApiMutation({
      queryKey: ['create-adset'],
      mutationFn: metaAdsManagerEndPoints.createAdset,
   });
   const createAdCreativeService = useApiMutation({
      queryKey: ['create-ad-creative'],
      mutationFn: metaAdsManagerEndPoints.createAdCreative,
   });
   const createAdService = useApiMutation({
      queryKey: ['create-ad'],
      mutationFn: metaAdsManagerEndPoints.createAd,
   });

   const generateCreativeService = useApiMutation({
      queryKey: ['generate-creative'],
      mutationFn: metaAdsManagerEndPoints.generateCreativeImage,
   });

   // const imageUploadToAzure=useApiMutation({
   //    queryKey:['upload-image'],
   //    mutationFn:metaAdsManagerEndPoints.getAzureImageUri
   // })

   const getSummary = useApiMutation({
      queryKey: ['summary'],
      mutationFn: metaAdsManagerEndPoints.getSummary,
   });

   const { data: sessionHistory, refetch: refetchSessionHistory } =
      useFetchMetaAdsAutoAgentHistoryBySessionQuery();

   const { mutateAsync: saveHistory } =
      useSaveMetaAdsAutoAgentHistoryMutation();

   const scrollToBottom = () => {
      if (scrollRef.current) {
         scrollRef.current.scrollIntoView({ behavior: 'smooth' });
      }
   };

   useEffect(() => {
      const checkIntegration = async () => {
         const result = await fetchMetaCredentials();

         setMetaCredentials(result);
      };

      void checkIntegration();
   }, []);

   useEffect(() => {
      if (summaryContent && !summaryLoading) {
         setTimeout(() => {
            scrollToBottom();
         }, 200);
      }
   }, [summaryContent, summaryLoading]);

   useEffect(() => {
      if (sessionHistory && sessionHistory.length > 0) {
         setIsHistorySession(true);
         const latestChat = sessionHistory[sessionHistory.length - 1];
         if (latestChat) {
            dispatch(setCampaignDetails(latestChat.campaign_details || null));
            dispatch(setAdsetDetails(latestChat.adset_details || null));
            dispatch(setAdsetData(latestChat.adset_data || null));
            dispatch(setAdCreativeId(latestChat.ad_creative_id || ''));
            dispatch(setAdId(latestChat.ad_id || ''));
            dispatch(setSummaryContent(latestChat.summary_content || null));
            dispatch(setStreamingMessages(latestChat.streaming_messages || []));
            dispatch(setStepStatuses(latestChat.step_statuses || []));
         }
      }
   }, [sessionHistory, dispatch]);

   const startAutomatedCampaignCreation = async () => {
      setFlowError(false);
      setIsHistorySession(false);
      const currentPrompt = userPrompt;
      setLastUserQuestion(currentPrompt);
      setShowSkeletonLoader(true);
      setTimeout(() => setShowSkeletonLoader(false), 900);
      dispatch(setUserPrompt(''));

      clearAnimationCache();

      if (!client_id) {
         toast({
            title: 'Error',
            description: 'User not authenticated. Please log in.',
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
         return;
      }
      if (!currentPrompt.trim()) {
         toast({
            title: 'Error',
            description: 'Please enter a campaign prompt.',
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
         return;
      }
      dispatch(setIsCreating(true));
      dispatch(setCurrentStep(1));

      try {
         dispatch(updateStepStatus({ stepIndex: 0, status: 'in-progress' }));
         const campaignMessageId = Date.now().toString();
         dispatch(
            addStreamingMessage({
               id: campaignMessageId,
               timestamp: new Date().toISOString(),
               type: 'campaign',
               title: ' Campaign Creation',
               content:
                  '[:checkbox:]  Analyzing your requirements and creating campaign strategy...',
               isStreaming: true,
               isComplete: false,
            }),
         );
         let payload: SendPromptPayload = {
            session_id: sessionId,
            user_query: currentPrompt,
            client_id,
            chat_level: 'campaign',
         };
         const campaignResponse = await sendPrompt(payload);
         if (!campaignResponse?.data) {
            throw new Error('Failed to get campaign response from AI');
         }
         const {
            name,
            objective,
            product_url,
            daily_budget,
            buying_type,
            campaign_budget_optimization,
         } = campaignResponse.data as ResponseCampaignData;

         const campaign = await createCampaignService.mutateAsync({
            client_id,
            name: name,
            objective:
               objective === 'LINK_CLICKS' ? 'OUTCOME_TRAFFIC' : objective,
            status: 'PAUSED',
            special_ad_categories: ['NONE'],
            buying_type: buying_type || 'AUCTION',
            campaign_budget_optimization: campaign_budget_optimization || true,
            daily_budget: Number(daily_budget) * 100,
         });
         if (!campaign?.id) {
            throw new Error('Failed to create campaign');
         }
         const campaign_details_payload: CampaignDetails = {
            name,
            campaign_id: campaign.id,
            objective,
            product_url: product_url,
            buying_type: buying_type || 'AUCTION',
            campaign_budget_optimization: campaign_budget_optimization || true,
            daily_budget: daily_budget,
         };

         dispatch(setCampaignDetails(campaign_details_payload));
         dispatch(updateStepStatus({ stepIndex: 0, status: 'completed' }));

         dispatch(
            updateStreamingMessage({
               id: campaignMessageId,
               updates: {
                  content: `[:checkbox:] Campaign "${name}" created successfully!\n\n [:moreDetails:]  Campaign Details:\n[:mark:] Objective: ${objective}\n[:mark:] Budget: ₹${daily_budget}/day\n[:mark:] Status: Paused\n[:mark:] ID: ${campaign.id}`,
                  isStreaming: false,
                  isComplete: true,
                  data: campaign_details_payload,
               },
            }),
         );

         dispatch(setCurrentStep(2));
         dispatch(updateStepStatus({ stepIndex: 1, status: 'in-progress' }));
         const adsetAnalysisMessageId = Date.now().toString();
         dispatch(
            addStreamingMessage({
               id: adsetAnalysisMessageId,
               timestamp: new Date().toISOString(),
               type: 'adset-analysis',
               title: ' Audience Analysis',
               content:
                  '[:laptop:]Running SQL queries to analyze historical performance data...\n\n[:laptop:] Processing Tool Call: Run SQL Query\n\n[:repeat:]Handing it over to Meta Ads Specialist\n\n Meta Ads Specalist :\n[:setting:] Setting up data filters\n[:secure:] Running a secure database query\n',
               isStreaming: true,
               isComplete: false,
            }),
         );
         payload = {
            session_id: sessionId,
            user_query: `Analyze audience for campaign "${name}"`,
            client_id,
            chat_level: 'adset-initial',
            product_url: product_url,
         };
         const adsetInitialResponse = await sendPrompt(payload);
         if (!adsetInitialResponse?.data) {
            throw new Error('Failed to get adset initial response from AI');
         }
         const {
            adset_name,
            bid_amount,
            targeting_analysis,
            billing_event,
            optimization_goal,
            promoted_object,
         } = adsetInitialResponse.data as unknown as ResponseAdSetData;
         if (!targeting_analysis) {
            throw new Error('Missing targeting analysis data');
         }
         const adsetDataObj: AdsetData = {
            adset_name: adset_name,
            bid_amount: bid_amount || 500,
            bid_strategy: campaign_details_payload.campaign_budget_optimization
               ? 'LOWEST_COST_WITH_BID_CAP'
               : 'LOWEST_COST_WITHOUT_CAP',
            billing_event: billing_event,
            optimization_goal: optimization_goal,
            targeting_analysis: targeting_analysis,
            ...(promoted_object && { promoted_object }),
         };
         dispatch(setAdsetData(adsetDataObj));
         dispatch(updateStepStatus({ stepIndex: 1, status: 'completed' }));
         const tables: TableSection[] =
            extractTargetingTables(targeting_analysis);
         dispatch(
            updateStreamingMessage({
               id: adsetAnalysisMessageId,
               updates: {
                  content: `[:checkbox:] Audience analysis completed!\n\n[:target:] Targeting Analysis:\n[:mark:] Age Range: ${targeting_analysis.age?.[0] ? Object.values(targeting_analysis.age[0])[0] : '25-45'}\n[:mark:]  Interests: ${
                     targeting_analysis.audience_interests
                        ?.slice(0, 2)
                        .map((i) => Object.values(i)[0])
                        .join(', ') || 'Fashion, Shopping'
                  }\n[:mark:]  Locations: ${targeting_analysis.geo_locations?.countries?.join(', ') || 'India'}\n[:mark:]  Placements: Facebook,Instagram`,
                  isStreaming: false,
                  isComplete: true,
                  tableData: tables,
                  data: adsetDataObj,
               },
            }),
         );

         dispatch(setCurrentStep(3));
         dispatch(updateStepStatus({ stepIndex: 2, status: 'in-progress' }));
         const adsetCreationMessageId = Date.now().toString();
         dispatch(
            addStreamingMessage({
               id: adsetCreationMessageId,
               timestamp: new Date().toISOString(),
               type: 'adset-creation',
               title: 'Adset Creation',
               content:
                  'Leveraging historical data for optimization...\n\n[:laptop:] Meta Ads Data Agent:\n[:tag:] Processing Tool Call: Transfer To Router\n\n[:target:] Creating optimized audience targeting based on analysis...',
               isStreaming: true,
               isComplete: false,
            }),
         );

         const transformedTargeting = {
            geo_locations: {
               countries: targeting_analysis.geo_locations?.countries || [],
            },
            age_range: targeting_analysis.age || [],
            audience_interests: targeting_analysis.audience_interests || [],
            placement_targeting: targeting_analysis.Placement || [],
            audience_behaviors: targeting_analysis.audience_behaviors || [],
            regions: targeting_analysis.region || [],
            cities: [],
         };
         payload = {
            session_id: sessionId,
            user_query: `Leverage Historical Data for Optimization`,
            client_id,
            chat_level: 'adset-final',
            product_url: product_url,
            targeting_analysis: transformedTargeting,
         };
         const adsetFinalResponse = await sendPrompt(payload);
         if (!adsetFinalResponse?.data) {
            throw new Error('Failed to get adset final response from AI');
         }
         const { targeting } =
            adsetFinalResponse.data as unknown as ResponseAdsetFinal;
         let updatedTargeting = JSON.parse(
            JSON.stringify(targeting),
         ) as targeting;

         updatedTargeting.publisher_platforms = ['instagram', 'facebook'];
         updatedTargeting.publisher_platforms =
            updatedTargeting.publisher_platforms?.map((p) => p.toLowerCase()) ||
            [];
         updatedTargeting.facebook_positions = ['feed'];
         updatedTargeting.facebook_positions =
            updatedTargeting.facebook_positions?.map((p) => p.toLowerCase()) ||
            [];
         updatedTargeting.instagram_positions = ['stream', 'explore', 'reels'];
         const ageData = targeting_analysis.age;
         const validAges = ageData.filter((entry) => {
            return entry.age !== 'Unknown' && typeof entry.roas === 'number';
         });
         const top3ByROAS = validAges
            .sort((a, b) => Number(b.roas) - Number(a.roas))
            .slice(0, 3);
         const ageRanges = top3ByROAS.map((entry) => {
            const [min, max] = entry.age.split('-').map(Number);
            return { min, max };
         });
         const ageMin = Math.min(...ageRanges.map((r) => r.min));
         const ageMax = Math.max(...ageRanges.map((r) => r.max));
         updatedTargeting = {
            ...updatedTargeting,
            age_min: ageMin,
            age_max: ageMax,
         };

         updatedTargeting.genders = [1, 2];

         const adsetResponse = await createAdsetService.mutateAsync({
            client_id,
            campaign_id: campaign_details_payload.campaign_id,
            name: adsetDataObj.adset_name || 'Default Adset Name',
            bid_amount: adsetDataObj.bid_amount || 500,
            bid_strategy:
               adsetDataObj.bid_strategy || 'LOWEST_COST_WITH_BID_CAP',
            billing_event: adsetDataObj.billing_event || 'IMPRESSIONS',
            optimization_goal: adsetDataObj.optimization_goal || 'LINK_CLICKS',
            ...(adsetDataObj.optimization_goal === 'OFFSITE_CONVERSIONS' &&
            adsetDataObj.promoted_object
               ? { promoted_object: adsetDataObj.promoted_object }
               : {}),
            targeting: updatedTargeting,
            status: 'PAUSED',
         });
         if (!adsetResponse?.id) {
            throw new Error('Failed to get adset ID from response');
         }
         const adset_details_payload: AdsetDetails = {
            adset_id: adsetResponse.id,
            adset_name: adsetDataObj.adset_name,
            bid_amount: adsetDataObj.bid_amount,
            targeting_analysis: updatedTargeting,
            options: {},
            chat_response: {},
            bid_strategy: adsetDataObj.bid_strategy || '',
            billing_event: adsetDataObj.billing_event || '',
            optimization_goal: adsetDataObj.optimization_goal || '',
            promoted_object: adsetDataObj.promoted_object,
         };
         dispatch(setAdsetDetails(adset_details_payload));
         dispatch(updateStepStatus({ stepIndex: 2, status: 'completed' }));

         dispatch(
            updateStreamingMessage({
               id: adsetCreationMessageId,
               updates: {
                  content: `[:checkbox:] Adset created successfully!\n\n[:target:] Adset Details:\n[:mark:] Name: ${adsetDataObj.adset_name}\n[:mark:] Bid Amount: ${adsetDataObj.bid_amount} Rupees\n[:mark:] Optimization Goal: ${adsetDataObj.optimization_goal}\n[:mark:] Billing Event: ${adsetDataObj.billing_event}\n[:mark:] Status: Active\n\n[:moreDetails:] Targeting Applied:\n[:mark:] Age: ${updatedTargeting.age_min}-${updatedTargeting.age_max}\n[:mark:] Interests: ${
                     updatedTargeting.interests
                        ?.slice(0, 2)
                        .map((i) => i.name)
                        .join(', ') || 'Fashion, Shopping'
                  }\n[:mark:] Locations: ${updatedTargeting.geo_locations?.countries?.join(', ') || 'India'}\n[:mark:] Platforms: ${updatedTargeting.publisher_platforms?.join(', ') || 'Facebook, Instagram'}`,
                  isStreaming: false,
                  isComplete: true,
                  data: adset_details_payload,
               },
            }),
         );

         toast({
            title: 'Adset Created',
            status: 'success',
            duration: 3000,
            isClosable: true,
         });

         dispatch(setCurrentStep(4));
         dispatch(updateStepStatus({ stepIndex: 3, status: 'in-progress' }));
         const adCreativeMessageId = Date.now().toString();
         dispatch(
            addStreamingMessage({
               id: adCreativeMessageId,
               timestamp: new Date().toISOString(),
               type: 'ad-creative',
               title: ' Ad Creative Generation',
               content:
                  'Generating compelling ad creative content...\n\n[:creative:] Creating visual assets and copy...\n[:creative:] Writing engaging ad text...\n[:image:] Generating product images...',
               isStreaming: true,
               isComplete: false,
            }),
         );
         payload = {
            session_id: sessionId,
            user_query: `Create ad creative for campaign "${name}"`,
            client_id,
            chat_level: 'ad-creative',
            product_url: product_url,
         };
         const adCreativeResponse = await sendPrompt(payload);
         if (!adCreativeResponse?.data) {
            throw new Error('Failed to get ad creative response from AI');
         }
         const {
            ad_creative_name,
            caption,
            image_url,
            description,
            recommendation,
         } = adCreativeResponse.data as ResponseAdCreativeData;

         //  console.log("before azure uplloading----------->")
         // const Azureuri= await imageUploadToAzure.mutateAsync(image_url);
         // console.log("uploaded image to Azure uri----------->",Azureuri)

         // Upload the image_url to Azure Blob and use the Azure URL
         // let azureImageUrl = image_url;
         // console.log('image Url',image_url)
         // if (image_url) {
         //    console.log("before uploading to azure-------->",image_url)
         //   const azureRes = await uploadImageUrlToAzure(image_url, 'ad-creative-image.jpg');
         //    console.log("before uploading to azure-------->",azureRes)
         //   if (azureRes.success && azureRes.uri) {
         //     azureImageUrl = azureRes.uri;
         //      console.log("before uploading to azure success-------->",azureImageUrl)
         //   }
         // }

         let generatedImageUrl: string | undefined = undefined;
         try {
            const { imageUrl: runwayImageUrl } =
               await generateCreativeService.mutateAsync({
                  caption,
                  description,
               });
            generatedImageUrl = runwayImageUrl;
         } catch (e) {
            console.warn(
               'generateCreativeService failed, falling back to image_url',
               e,
            );
         }
         const finalImageUrl = generatedImageUrl || image_url;

         const response = await metaAdsManagerEndPoints.fetchPageId({
            client_id: client_id,
         });
         const pageId = response.data;
         const object_story_spec = {
            page_id: pageId,
            link_data: {
               link: product_url,
               message: caption,
               name: ad_creative_name,
               description: description,
               picture: finalImageUrl,
               call_to_action: {
                  type: 'SHOP_NOW',
                  value: {
                     link: product_url,
                  },
               },
            },
         };

         const adCreativeApiResponse =
            await createAdCreativeService.mutateAsync({
               client_id,
               name: ad_creative_name || `Ad_${Date.now()}`,
               object_story_spec,
            });
         if (!adCreativeApiResponse?.id) {
            throw new Error('Failed to create ad creative');
         }

         dispatch(setAdCreativeId(adCreativeApiResponse.id));
         dispatch(updateStepStatus({ stepIndex: 3, status: 'completed' }));

         dispatch(
            updateStreamingMessage({
               id: adCreativeMessageId,
               updates: {
                  content: `[:checkbox:] Ad creative generated successfully!\n\n[:creative:] Creative Details:\n[:mark:] Name: ${ad_creative_name}\n[:mark:] Caption: ${caption}\n[:mark:] Description: ${description}\n[:mark:] Call to Action: Shop Now\n[:mark:] Creative ID: ${adCreativeApiResponse.id}\n\n[:bulb:]   Recommendation:\n [:mark:]  ${recommendation}`,
                  isStreaming: false,
                  isComplete: true,
                  imageUrl: finalImageUrl,
                  type: 'ad-creative',
                  data: {
                     ad_creative_name,
                     caption,
                     imageUrl: finalImageUrl,
                     description,
                     recommendation,
                  },
               },
            }),
         );

         toast({
            title: 'Ad Creative Created',
            status: 'success',
            duration: 3000,
            isClosable: true,
         });

         dispatch(setCurrentStep(4));
         dispatch(updateStepStatus({ stepIndex: 4, status: 'in-progress' }));
         const adGenerationMessageId = Date.now().toString();
         dispatch(
            addStreamingMessage({
               id: adGenerationMessageId,
               timestamp: new Date().toISOString(),
               type: 'ad',
               title: 'Ad  Generation',
               content:
                  '[:laptop:] Crafting high-performing ad creatives...\n\n[:target:] Strategizing audience messaging...\n[:creative:] Designing eye-catching visuals...\n[:creative:] Writing persuasive copy...\n[:image:] Generating product images...',
               isStreaming: true,
               isComplete: false,
            }),
         );

         const Adpayload: CreateAdPayload = {
            client_id,
            name: `Ad_${Date.now()}`,
            adset_id: adset_details_payload.adset_id,
            creative: { creative_id: adCreativeApiResponse.id },
            status: 'PAUSED',
         };
         const adResponse = await createAdService.mutateAsync(Adpayload);
         if (!adResponse.id) {
            throw new Error('Failed to create Ad');
         }
         dispatch(setAdId(adResponse.id));
         dispatch(updateStepStatus({ stepIndex: 4, status: 'completed' }));

         dispatch(
            updateStreamingMessage({
               id: adGenerationMessageId,
               updates: {
                  content: `[:checkbox:] Ad Generated successfully!\n\n[:target:] Ad Details:\n[:mark:] Name: ${Adpayload.name}\n[:mark:] Ad Id:${adResponse.id}`,
                  isStreaming: false,
                  isComplete: true,
                  data: adset_details_payload,
               },
            }),
         );

         toast({
            title: 'Ad Creation Complete! 🎉',
            status: 'success',
            duration: 10000,
            isClosable: true,
         });

         const summaryPayload = {
            campaignDetails: campaign_details_payload,
            adsetDetails: adset_details_payload,
            adCreativeDetails: {
               name: ad_creative_name,
               object_story_spec: object_story_spec,
            },
            adDetails: {
               adId: adResponse.id,
               adName: Adpayload.name,
            },
         };

         try {
            const summaryRes = await getSummary.mutateAsync(summaryPayload);

            if (summaryRes) {
               dispatch(setSummaryContent(summaryRes.summary));

               await new Promise((resolve) => setTimeout(resolve, 100));
            }
         } catch (e) {
            toast({
               title: 'Summary Error',
               description: 'Failed to get campaign summary.',
               status: 'error',
               duration: 5000,
               isClosable: true,
            });
            dispatch(setSummaryContent('Failed to get Summary'));

            await new Promise((resolve) => setTimeout(resolve, 100));
         } finally {
            dispatch(setSummaryLoading(false));
         }
      } catch (error) {
         console.error('Automated campaign creation failed:', error);
         setFlowError(true);
         if (currentStep > 0) {
            dispatch(
               updateStepStatus({
                  stepIndex: currentStep - 1,
                  status: 'error',
               }),
            );
         }
         toast({
            title: `Campaign Creation Failed at Step ${currentStep}`,
            description:
               error instanceof Error
                  ? error.message
                  : 'An unexpected error occurred',
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      } finally {
         dispatch(setIsCreating(false));

         try {
            const { client_id, user_id } = userDetails || {};
            if (client_id && user_id) {
               const chatId = Date.now().toString();

               const state = latestStateRef.current || {};

               const {
                  campaignDetails: latestCampaignDetails,
                  adsetDetails: latestAdsetDetails,
                  adsetData: latestAdsetData,
                  adCreativeId: latestAdCreativeId,
                  adId: latestAdId,
                  summaryContent: latestSummaryContent,
                  streamingMessages: latestStreamingMessages,
                  stepStatuses: latestStepStatuses,
                  userPrompt: latestUserPrompt,
                  sessionId: latestSessionId,
               } = state;

               const finalizedMessages = Array.isArray(latestStreamingMessages)
                  ? latestStreamingMessages.filter((msg) => msg.isComplete)
                  : [];

               const finalSummaryContent =
                  latestSummaryContent ||
                  'Campaign creation completed successfully';

               await saveHistory({
                  client_id,
                  user_id,
                  session_id: latestSessionId || sessionId,
                  chat_id: chatId,
                  user_query: latestUserPrompt || userPrompt,
                  final_response: 'Campaign creation completed successfully',
                  campaign_details: latestCampaignDetails || undefined,
                  adset_details: latestAdsetDetails || undefined,
                  adset_data: latestAdsetData || undefined,
                  ad_creative_id: latestAdCreativeId || undefined,
                  ad_id: latestAdId || undefined,
                  summary_content: finalSummaryContent,
                  streaming_messages: finalizedMessages,
                  step_statuses: latestStepStatuses,
               });
               await refetchSessionHistory();
            }
         } catch (historyError) {
            console.error('Failed to save history:', historyError);
         }
      }
   };
   useEffect(() => {
      if (chatEndRef.current) {
         chatEndRef.current.scrollIntoView({ behavior: 'smooth' });
      }
   }, [streamingMessages]);

   useEffect(() => {
      return () => {
         if (streamingMessages.length === 0) {
            clearAnimationCache();
         }
      };
   }, [streamingMessages.length]);

   return (
      <div className='w-full h-full flex flex-col'>
         <div className='flex-1 flex flex-col mt-2 px-6 overflow-y-auto'>
            <div className='max-w-7xl w-full mx-auto'>
               <div className='flex justify-between items-center mb-4'>
                  {!isCreating && streamingMessages.length === 0 ? (
                     <div className='w-full flex justify-center'>
                        <div className='flex bg-[#E4F2FF] rounded-[16px] p-1 w-fit shadow-sm border border-[#E0E7EF]'>
                           <button
                              className={`flex items-center gap-2 px-6 py-2 rounded-[12px] text-base font-medium transition-colors duration-150 focus:outline-none ${!isAutomationMode ? 'bg-white text-[#111] shadow border border-[#D1D5DB]' : 'text-[#333333]'} cursor-pointer`}
                              onClick={() => {
                                 setIsAutomationMode(false);
                                 navigate('/marco/meta-ads-manager-agent');
                              }}
                              type='button'
                           >
                              <img
                                 src={manualIcon}
                                 alt='Manual Mode'
                                 className='w-5 h-5'
                              />
                              Interactive Mode
                           </button>
                           <button
                              className={`flex items-center gap-2 px-6 py-2 rounded-[12px] text-base font-medium transition-colors duration-150 focus:outline-none ml-1 ${isAutomationMode ? 'bg-white  shadow border border-[#2563EB] text-[#3C76E1]' : 'text-[#6B7280]'} cursor-pointer`}
                              style={{
                                 boxShadow: isAutomationMode
                                    ? '0 0 0 1px #3C76E1'
                                    : 'none',
                              }}
                              onClick={() => {
                                 setIsAutomationMode(true);
                                 navigate('/marco/meta-ads-manager-auto');
                              }}
                              type='button'
                           >
                              <img
                                 src={isAutomationMode ? autoIcon2 : autoIcon}
                                 alt='Auto Mode'
                                 className='w-5 h-5'
                              />
                              AutoPilot
                           </button>
                        </div>
                     </div>
                  ) : (
                     <div />
                  )}
               </div>

               {!isCreating && streamingMessages.length === 0 && (
                  <div className='max-w-4xl mx-auto'>
                     <div className='w-full flex justify-center '>
                        <div className='w-40 h-40 overflow-hidden'>
                           <img
                              src={MetaAdsAgentImage}
                              alt='cropped'
                              className='w-36'
                           />
                        </div>
                     </div>
                     <div className='flex justify-center mb-1'>
                        <p className='w-full  text-center font-bold text-[8px] md:text-[20px] text-black '>
                           Meta Ads Manager Agent
                        </p>
                     </div>
                     <div className='flex justify-center'>
                        <p className='w-1/2 text-center text-sm text-gray-700 max-w-xl'>
                           Hello! Welcome to your Meta Ads Manager Agent. I'm
                           here to help you create, manage, and optimize your ad
                           campaigns automatically. Just enter a single prompt
                           and I'll handle the entire campaign creation process!
                        </p>
                     </div>
                     <div className='flex justify-center w-full mt-1'>
                        <div
                           className='inline-flex items-center gap-2 px-3 py-[2px] rounded-xs border-2 border-[#3C76E1] bg-[#EAF3FF] mb-2'
                           style={{
                              boxShadow: '0 0 0 1px #3C76E1',
                           }}
                        >
                           <BsExclamationTriangleFill
                              color='#3C76E1'
                              size={16}
                           />
                           <span className='text-[#3C76E1] font-medium text-sm'>
                              Required for Auto Mode: Campaign Name, Objective,
                              Website URL and Budget
                           </span>
                        </div>
                     </div>

                     <div className='flex flex-wrap gap-2 items-center justify-center max-w-[900px] text-[12px] md:text-[14px] mx-4 mb-10'>
                        {copilot_prompts.map((question, idx) => (
                           <React.Fragment key={idx}>
                              <Tooltips
                                 content={question}
                                 className='bg-gray-500 text-white w-max max-w-[400px] fit-c para5'
                              >
                                 <div
                                    className='flex-1 w-full md:w-[180px] md:min-h-[95px] max-h-[95px] border-[1px] border-gray-300 text-left rounded-[15px] px-[15px] py-[5px] cursor-pointer shadow-md font-semibold bg-white hover:bg-gray-100 hover:text-black line-clamp-4 overflow-hidden text-ellipsis'
                                    onClick={() => {
                                       if (Metacredentials !== false) {
                                          dispatch(setUserPrompt(question));
                                       }
                                    }}
                                 >
                                    {question}
                                 </div>
                              </Tooltips>
                           </React.Fragment>
                        ))}
                     </div>
                  </div>
               )}
               {(isCreating || streamingMessages.length > 0) && (
                  <div className='max-w-5xl mx-auto'>
                     <div className='text-center mb-8'>
                        <div className='flex items-center justify-center gap-4 mb-4'></div>
                     </div>

                     {isHistorySession &&
                     sessionHistory &&
                     sessionHistory.length > 0 ? (
                        <div className='flex justify-end w-[100%] mt-2 mb-5'>
                           <div className='w-[fit-content] max-w-[85%] md:max-w-[65%] bg-[#3444AE] text-white p-[12px_14px] text-left rounded-l-[15px] rounded-r-[0px] rounded-b-[15px] text-[15px] relative inline-block'>
                              <p className='text-[14px] md:text-[16px]'>
                                 {sessionHistory[0].user_query}
                              </p>
                           </div>
                        </div>
                     ) : lastUserQuestion ? (
                        <div className='flex justify-end w-[100%] mt-2 mb-5'>
                           <div className='w-[fit-content] max-w-[85%] md:max-w-[65%] bg-[#3444AE] text-white p-[12px_14px] text-left rounded-l-[15px] rounded-r-[0px] rounded-b-[15px] text-[15px] relative inline-block'>
                              <p className='text-[14px] md:text-[16px]'>
                                 {lastUserQuestion}
                              </p>
                           </div>
                        </div>
                     ) : null}

                     {!isHistorySession && showSkeletonLoader && (
                        <div className='mb-2 flex justify-start'>
                           <div className='w-[80%] max-w-[600px]'>
                              <div className='h-4 bg-gray-200 rounded mb-2 animate-pulse'></div>
                              <div className='h-4 bg-gray-200 rounded mb-2 animate-pulse w-2/3'></div>
                              <div className='h-4 bg-gray-200 rounded mb-2 animate-pulse w-1/2'></div>
                           </div>
                        </div>
                     )}

                     {(isHistorySession || !showSkeletonLoader) && (
                        <>
                           <StreamingMessageTimeline
                              streamingMessages={streamingMessages}
                              stepStatuses={stepStatuses}
                              activeStreamingIndex={activeStreamingIndex}
                              setActiveStreamingIndex={setActiveStreamingIndex}
                              scrollRef={chatEndRef}
                              disableAnimation={isHistorySession}
                           />

                           {isHistorySession ? (
                              <>
                                 <SummaryAgentMessage
                                    summary={summaryContent}
                                    loading={summaryLoading}
                                    scrollRef={chatEndRef}
                                    disableAnimation={true}
                                 />
                                 <CampaignSummaryCard
                                    campaignDetails={reduxState.campaignDetails}
                                    adsetDetails={reduxState.adsetDetails}
                                 />
                              </>
                           ) : (
                              (summaryLoading || summaryContent) &&
                              activeStreamingIndex >=
                                 streamingMessages.length &&
                              !flowError && (
                                 <>
                                    <SummaryAgentMessage
                                       summary={summaryContent}
                                       loading={summaryLoading}
                                       scrollRef={chatEndRef}
                                       disableAnimation={false}
                                       onType={() => {
                                          setTimeout(() => {
                                             if (chatEndRef.current) {
                                                chatEndRef.current.scrollIntoView(
                                                   {
                                                      behavior: 'smooth',
                                                      block: 'end',
                                                      inline: 'nearest',
                                                   },
                                                );
                                             }
                                          }, 20);
                                       }}
                                       onComplete={() => {
                                          setTimeout(() => {
                                             if (chatEndRef.current) {
                                                chatEndRef.current.scrollIntoView(
                                                   {
                                                      behavior: 'smooth',
                                                      block: 'end',
                                                      inline: 'nearest',
                                                   },
                                                );
                                             }
                                          }, 100);
                                       }}
                                    />
                                    {!summaryLoading && summaryContent && (
                                       <CampaignSummaryCard
                                          campaignDetails={
                                             reduxState.campaignDetails
                                          }
                                          adsetDetails={reduxState.adsetDetails}
                                       />
                                    )}
                                 </>
                              )
                           )}

                           <div ref={chatEndRef}></div>
                        </>
                     )}
                  </div>
               )}
            </div>
         </div>

         {Metacredentials === false && (
            <div
               className='bg-red-100  text-red-600 rounded-xs w-fit mx-auto text-center px-2 py-2'
               style={{
                  boxShadow: '0 0 0 1px red',
               }}
            >
               <p className='flex items-center gap-2 font-medium'>
                  <BsExclamationTriangleFill color='red' size={16} />
                  Please provide Meta credentials to start creating Campaign
               </p>
            </div>
         )}
         <div className='flex flex-col items-center w-[95%] max-w-[1700px] my-[10px] md:my-[20px] flex-nowrap justify-between gap-2 bg-white ml-8'>
            <div className=' flex flex-col items-center w-[95%] max-w-[900px] my-[10px]'>
               <div className='w-full shadow-md rounded-xl border-2 p-0   '>
                  <Textarea
                     rows={1}
                     placeholder='Type your prompt here'
                     className='resize-none max-h-[150px] overflow-auto !text-[12px] md:!text-[16px] shadow-none border-none focus:ring-0 focus:border-none :hover:border-none focus-visible:border-0 focus-visible:ring-0 focus-visible:outline-none'
                     value={userPrompt}
                     disabled={isCreating || !Metacredentials}
                     onChange={(e) => dispatch(setUserPrompt(e.target.value))}
                     onKeyDown={(e) => {
                        if (
                           e.key === 'Enter' &&
                           !e.shiftKey &&
                           !isCreating &&
                           userPrompt.trim()
                        ) {
                           e.preventDefault();
                           void startAutomatedCampaignCreation();
                        }
                     }}
                  />
                  <div className='flex flex-row items-center justify-between rounded-xl px-2 py-3 bg-white mt-2 '>
                     <Tabs
                        defaultValue={isAutomationMode ? 'auto' : 'manual'}
                        value={isAutomationMode ? 'auto' : 'manual'}
                        onValueChange={(value) => {
                           const isAuto = value === 'auto';
                           setIsAutomationMode(isAuto);
                           navigate(
                              isAuto
                                 ? '/marco/meta-ads-manager-auto'
                                 : '/marco/meta-ads-manager-agent',
                           );
                        }}
                     >
                        <TabsList className='h-[50px] w-[80px] rounded-md bg-[#E4F2FF] gap-[3px] flex flex-row items-center'>
                           <HoverCard openDelay={200} closeDelay={200}>
                              <HoverCardTrigger className='w-[45px] h-[50px] p-0'>
                                 <TabsTrigger
                                    value='manual'
                                    className={cn(
                                       defaultTabTriggerClass,
                                       'rounded-md w-full hover:cursor-pointer data-[state=inactive]:border-none data-[state=active]:!border-[#3C76E1] data-[state=active]:!bg-[#ffffff]',
                                    )}
                                 >
                                    <img
                                       src={manualIcon2}
                                       alt='Manual Mode'
                                       className='w-[25px] h-[25px] block'
                                    />
                                 </TabsTrigger>
                              </HoverCardTrigger>
                              <HoverCardContent className='bg-black w-[280px] text-white'>
                                 <p className='text-md'>Interactive Mode</p>
                                 <Separator className='my-2' />
                                 <p className='text-sm'>
                                    Review and manage campaign settings
                                    step-by-step.
                                 </p>
                                 <HoverCardArrow className='fill-black' />
                              </HoverCardContent>
                           </HoverCard>

                           <HoverCard openDelay={200} closeDelay={200}>
                              <HoverCardTrigger className='w-[50px] h-[45px] p-0'>
                                 <TabsTrigger
                                    value='auto'
                                    className={cn(
                                       defaultTabTriggerClass,
                                       'rounded-md w-full hover:cursor-pointer data-[state=inactive]:border-none data-[state=active]:!border-[#3C76E1] data-[state=active]:!bg-[#ffffff]',
                                    )}
                                 >
                                    <img
                                       src={
                                          isAutomationMode
                                             ? autoIcon2
                                             : autoIcon
                                       }
                                       alt='Auto Mode'
                                       className='w-[20px] h-[20px] block'
                                    />
                                 </TabsTrigger>
                              </HoverCardTrigger>
                              <HoverCardContent className='bg-black w-[280px] text-white'>
                                 <p className='text-md'>AutoPilot</p>
                                 <Separator className='my-2' />
                                 <p className='text-sm'>
                                    Agent handles full campaign creation
                                    automatically end to end.
                                 </p>
                                 <HoverCardArrow className='fill-black' />
                              </HoverCardContent>
                           </HoverCard>
                        </TabsList>
                     </Tabs>
                     <Button
                        disabled={isCreating || !userPrompt.trim()}
                        onClick={() => void startAutomatedCampaignCreation()}
                        className='w-[40px] h-[40px] bg-[#437eeb] text-white rounded-md shadow-md hover:bg-[#3c76e1] disabled:opacity-50 hover:cursor-pointer ml-2 flex items-center justify-center'
                     >
                        <IoSend style={{ height: 24, width: 24 }} />
                     </Button>
                  </div>
               </div>
               <p className='p-0 m-0 text-[10px] md:text-[12px] text-gray-500 text-center  '>
                  Agent can make mistakes. Please double-check responses.
               </p>
            </div>
         </div>
      </div>
   );
};
export default MetaAdsManagerAuto;
