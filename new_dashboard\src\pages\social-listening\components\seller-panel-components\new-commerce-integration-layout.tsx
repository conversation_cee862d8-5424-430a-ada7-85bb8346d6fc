import React, { ReactNode } from 'react';
import { useNavigate } from 'react-router-dom';
import { Arrow<PERSON>ef<PERSON>, CheckCircle2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
//import { Separator } from '@/components/ui/separator';
import { cn } from '@/utils';

interface IntegrationStep {
   title: string;
   description: string;
   isCompleted?: boolean;
}

interface ModernIntegrationWrapperProps {
   title: string;
   description: string;
   logo: string;
   logoAlt: string;
   steps: string[];
   children: ReactNode;
   className?: string;
}

const parseStepContent = (stepText: string): IntegrationStep => {
   const description = stepText
      .replace(/<([^:]+):([^>]+)>/g, '$1')
      .replace(/\n/g, ' ')
      .trim();

   return {
      title: description,
      description: '',
      isCompleted: true,
   };
};

const IntegrationSteps: React.FC<{ steps: IntegrationStep[] }> = ({
   steps,
}) => {
   return (
      <div className='space-y-4'>
         <div className='relative'>
            {steps.map((step, index) => (
               <div key={index} className='flex gap-4 group relative'>
                  {/* Dotted line connector */}
                  {index < steps.length - 1 && (
                     <div
                        className='absolute left-2.5 top-7 w-0.5 h-8 border-l-2 border-dotted border-green-400'
                        style={{ transform: 'translateX(-1px)' }}
                     />
                  )}

                  <div className='flex-shrink-0 mt-1 relative z-10'>
                     <CheckCircle2 className='h-5 w-5 text-green-600 bg-white' />
                  </div>
                  <div className='flex-1 min-w-0 pb-6'>
                     <p className='text-sm text-gray-700 leading-relaxed font-medium'>
                        {step.title}
                     </p>
                  </div>
               </div>
            ))}
         </div>
      </div>
   );
};

const IntegrationHeader: React.FC<{
   logo: string;
   logoAlt: string;
   title: string;
   description: string;
}> = ({ logo, logoAlt, title, description }) => {
   return (
      <div className='flex items-center gap-4 mb-4'>
         <div className='flex-shrink-0'>
            <div className='w-19 h-19 flex items-center justify-center p-2'>
               <img
                  src={logo}
                  alt={logoAlt}
                  className='max-w-full max-h-full object-contain'
               />
            </div>
         </div>
         <div className='flex-1'>
            <h2 className='text-xl font-semibold text-gray-900 mb-2'>
               {title}
            </h2>
            <p className='text-sm text-gray-600 leading-relaxed'>
               {description}
            </p>
            <Badge
               variant='secondary'
               className='mt-3 bg-purple-100 text-purple-700 hover:bg-purple-200'
            >
               Integration Setup
            </Badge>
         </div>
      </div>
   );
};

export const NewCommerceIntegrationLayout: React.FC<
   ModernIntegrationWrapperProps
> = ({ title, description, logo, logoAlt, steps, children, className }) => {
   const navigate = useNavigate();

   const parsedSteps = steps.map((step) => parseStepContent(step));

   const handleGoBack = () => {
      navigate(-1);
   };

   return (
      <div className={cn('h-full bg-gray-50 overflow', className)}>
         <div className='max-w-7xl mx-auto p-4'>
            <Button
               variant='ghost'
               onClick={handleGoBack}
               className='mb-2 text-gray-900 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200 px-3 py-2 rounded-md'
            >
               <ArrowLeft className='h-6 w-6 mr-2' />
               Back to Integrations
            </Button>

            <div className='grid grid-cols-1 lg:grid-cols-2 gap-4 items-start'>
               <div className='space-y-6'>
                  <Card className='border-0 shadow-lg bg-white rounded-xl'>
                     <CardHeader className='pb-0'>
                        <CardTitle className='text-xl font-semibold text-gray-900 flex items-center gap-2'>
                           Setup Instructions
                        </CardTitle>
                     </CardHeader>
                     <CardContent className='pt-0 '>
                        <IntegrationSteps steps={parsedSteps} />
                     </CardContent>
                  </Card>
               </div>

               <div className='space-y-6'>
                  <Card className='border-0 shadow-lg bg-white rounded-xl h-full min-h-[500px]'>
                     <CardHeader>
                        <IntegrationHeader
                           logo={logo}
                           logoAlt={logoAlt}
                           title={title}
                           description={description}
                        />
                     </CardHeader>
                     <CardContent className='pt-3 pb-6'>{children}</CardContent>
                  </Card>
               </div>
            </div>
         </div>
      </div>
   );
};
