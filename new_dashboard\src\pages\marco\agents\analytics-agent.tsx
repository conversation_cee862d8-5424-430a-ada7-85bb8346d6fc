import React from 'react';
import {
   setCurrentSessionID,
   setChunks,
   clearKpiPrompts,
   setKpiPromptsProcessed,
   setCurrentMode,
} from '../../../store/reducer/analytics-agent-reducer';
import AnalyticAgentChat from '../components/chat-bubble/analytic-agent-chat';
import {
   AnalyticsAgentChat,
   startDirectResponse,
} from '../../../api/service/agentic-workflow/analytics-agent';
import analyticAgentIcon from '../../../assets/image/agents/Salma - Analytics Agent.png';
import aiCMOAgentIcon from '../../../assets/image/agents/Motoko - AI CMO.png';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import {
   CMO_QUESTION_SUGGESTIONS,
   DEFAULT_QUESTION_SUGGESTIONS,
   ERROR_MESSAGES,
} from '../utils/analytics-agent/constants';
import store, { useAppDispatch, useAppSelector } from '../../../store/store';
import { useEffect, useRef, useState } from 'react';
import { IoAnalyticsSharp } from 'react-icons/io5';
import { AuthUser } from '../../../types/auth';
import {
   useStreamResponse,
   useAddToSessionHistoryMutation,
   useFetchSessionHistoryQuery,
   useAddInsightsToSessionHistoryMutation,
   useFetchSessionInsights,
   useTrackFeatureUsageMutation,
   useFetchFeatureUsageQuery,
   useFetchAnalyticsHistoryQuery,
} from '../apis/analytics-agent-apis';
import { getSmartSuggestions } from '../utils/analytics-agent/helpers';
import { toast } from 'sonner';
import { useSearchParams } from 'react-router-dom';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { PiChartBar } from 'react-icons/pi';
import CMOModeIcon from '@/assets/icons/cmo-mode-icon.svg';
import {
   HoverCard,
   HoverCardContent,
   HoverCardTrigger,
} from '@/components/ui/hover-card';
import { HoverCardArrow } from '@radix-ui/react-hover-card';
import { Separator } from '@/components/ui/separator';
import Tooltips from '@/components/tooltip';
import { cn } from '@/utils';
import {
   defaultTabListClass,
   defaultTabTriggerClass,
} from '@/constants/defaultClasses';
import { useCreateNotificationMutation } from '@/components/notifications-popover/notifications-apis';
import { useQueryClient } from '@tanstack/react-query';
import { setCurrentHistory } from '@/store/reducer/marco-reducer';

const USAGE_LIMITS: Record<string, number> = {
   data_analyst: 100,
   cmo: 20,
};

interface KpiMetadata {
   kpi: string;
   currency: string;
   start_date: string;
   end_date: string;
   prev_start_date: string;
   prev_end_date: string;
   platform: string;
   percentage_change?: string;
   campaign_id?: string;
   campaign_name?: string;

   mode?: string;
   action_type?: string;
   source?: string;
}

const AnalyticsAgent = () => {
   const dispatch = useAppDispatch();
   const queryClient = useQueryClient();
   const getState = store.getState.bind(store);

   const [searchParams] = useSearchParams();
   const paramsQuery = searchParams.get('query') || '';
   const paramsAiPrompt = searchParams.get('aiPrompt') || '';
   const routedFromAgent = searchParams.get('routedFrom') || '';

   const originalRoutedFromAgent = React.useMemo(
      () => routedFromAgent,
      [routedFromAgent],
   );

   const getKpiMetadata = (): KpiMetadata | null => {
      const metadata = sessionStorage.getItem('kpi_diagnostics_metadata');

      if (metadata) {
         try {
            const parsedMetadata = JSON.parse(metadata) as KpiMetadata;
            return parsedMetadata;
         } catch (error) {
            console.error(
               'Error parsing kpiMetadata from sessionStorage:',
               error,
            );
            return null;
         }
      }
      return null;
   };

   const { client_id, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) ?? {};
   const messagesEndRef = useRef<HTMLDivElement>(null);

   const {
      chunks,
      currentSessionID,
      currentMode,
      notificationPermission,
      kpiDisplayPrompt,
      kpiAiPrompt,
      kpiPromptsProcessed,
      directResponse,
   } = useAppSelector((state) => state.analyticsAgent);
   const { connectionDetails } = useAppSelector((state) => state.media);

   const [prompt, setPrompt] = useState<string>('');
   const [suggestions, setSuggestions] = useState<string[]>([]);
   const [queryRunning, setQueryRunning] = useState<boolean>(false);
   const [routedFrom, setRoutedFrom] = useState<
      'alerting-agent' | 'diagnostic-agent' | null
   >(null);

   const { refetch: refetchAnalyticshistory } = useFetchAnalyticsHistoryQuery();

   const {
      data: currentSessionHistory,
      isFetching: isFetchingSessionHistory,
      refetch: refetchSessionHistory,
   } = useFetchSessionHistoryQuery();

   const {
      data: sessionInsights,
      isFetching: isFetchingSessionInsights,
      refetch: refetchSessionInsights,
   } = useFetchSessionInsights();

   const { data: featureUsage, refetch: refetchFeatureUsage } =
      useFetchFeatureUsageQuery();

   const {
      mutateAsync: addToSessionHistory,
      isPending: isPendingAddToSessionHistory,
   } = useAddToSessionHistoryMutation();

   const {
      mutateAsync: addInsightsToSessionHistory,
      isPending: isPendingAddInsightsToSessionHistory,
   } = useAddInsightsToSessionHistoryMutation();

   const { mutateAsync: updateFeatureUsage } = useTrackFeatureUsageMutation();

   const { mutateAsync: sendNotification } = useCreateNotificationMutation();

   const handlePromptChange = (
      event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
   ) => {
      setPrompt(event.target.value);
   };

   const handleKeyDown = async (
      event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>,
   ) => {
      if (event.key === 'Enter') {
         event.preventDefault();
         await handleSendPrompt();
      }
   };

   const { startStream, isStreaming } = useStreamResponse(dispatch, getState);

   const handleSendPrompt = async (
      userVisiblePrompt?: string,
      actualAiPrompt?: string,
      routedFromAgent: 'alerting-agent' | 'diagnostic-agent' | null = null,
   ) => {
      if (
         isPendingAddToSessionHistory ||
         isPendingAddInsightsToSessionHistory
      ) {
         toast.error('Please wait for the current request to complete.');
         return;
      }

      setQueryRunning(true);

      const kpiMetadata = getKpiMetadata();

      const clearKpiMetadata = () => {
         sessionStorage.removeItem('kpi_diagnostics_metadata');
      };

      const isRewriteRequest =
         !kpiMetadata && !kpiDisplayPrompt && !kpiAiPrompt && userVisiblePrompt;
      let rewriteKpiMetadata: KpiMetadata | null = null;

      if (
         isRewriteRequest &&
         currentSessionHistory &&
         currentSessionHistory.length > 0
      ) {
         const chatWithMetadata = currentSessionHistory
            .sort(
               (a, b) =>
                  new Date(b.created_at).getTime() -
                  new Date(a.created_at).getTime(),
            )
            .find(
               (chat) =>
                  chat.diagnostics_prompt_meta_data &&
                  Object.keys(chat.diagnostics_prompt_meta_data).length > 0,
            );

         if (chatWithMetadata?.diagnostics_prompt_meta_data) {
            rewriteKpiMetadata =
               chatWithMetadata.diagnostics_prompt_meta_data as unknown as KpiMetadata;
         }
      }

      const displayText = kpiDisplayPrompt || userVisiblePrompt || prompt;
      const aiText = kpiAiPrompt || actualAiPrompt || displayText;

      if (!displayText || !aiText) {
         toast.error('Please enter your query.');
         setPrompt('');
         setQueryRunning(false);
         return;
      }

      if (
         featureUsage &&
         featureUsage.length > 0 &&
         featureUsage.find((obj) => obj.mode === currentMode) &&
         !featureUsage.find((obj) => obj.mode === currentMode)?.is_enabled
      ) {
         toast.error(
            `Your free limit has expired for ${currentMode
               .split('-')
               .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
               .join(
                  ' ',
               )} mode. Please contact Flable support and upgrade to continue using the Analytics Agent.`,
         );
         setQueryRunning(false);
         return;
      }

      const sessionId = !currentSessionID ? `Z${Date.now()}` : currentSessionID;
      dispatch(setCurrentSessionID(sessionId));

      const newChatId = `${new Date().getTime()}`;
      setPrompt('');

      const addToSessionHistoryPayload = {
         client_id: String(client_id),
         user_id: String(user_id),
         session_id: String(sessionId),
         chat_id: String(newChatId),
         user_query: displayText,
         response_status: 'pending' as 'success' | 'error' | 'pending',
         channel: '',
         final_response: '',
         agent_name: '',
         prev_agent_response: '',
         prev_agent_name: '',
         context_snapshot: {},
         response_like_dislike: '',
         response_feedback_category: '',
         rewrite_response: false,
         copy_response: false,
         response_time: 0,
         user_feedback_comments: '',
         question_mode: (() => {
            if (!currentSessionID) {
               if (
                  originalRoutedFromAgent &&
                  originalRoutedFromAgent.includes('diagnostic-agent/cmo')
               ) {
                  return 'diagnostic-agent/cmo';
               } else if (
                  originalRoutedFromAgent &&
                  originalRoutedFromAgent.includes(
                     'diagnostic-agent/data-analyst',
                  )
               ) {
                  return 'diagnostic-agent/data-analyst';
               } else {
                  return currentMode;
               }
            }

            if (currentSessionHistory && currentSessionHistory.length > 0) {
               const firstMessage = currentSessionHistory[0];
               const existingModes = firstMessage.question_modes || [];

               const isModeAlreadyCovered = existingModes.some((mode) => {
                  if (mode.startsWith('diagnostic-agent/')) {
                     const subModes = mode.split('/')[1]?.split(',') || [];
                     return subModes.includes(currentMode);
                  }
                  return mode === currentMode;
               });

               if (!isModeAlreadyCovered) {
                  return currentMode;
               } else {
                  return '';
               }
            }

            return '';
         })(),
         diagnostics_prompt_meta_data: (() => {
            if (!kpiMetadata && !rewriteKpiMetadata) {
               return {} as Record<string, string>;
            }

            const metadata = kpiMetadata || rewriteKpiMetadata;

            const baseData: Record<string, string> = {
               kpi: String(metadata?.kpi || ''),
               currency: String(metadata?.currency || ''),
               start_date: String(metadata?.start_date || ''),
               end_date: String(metadata?.end_date || ''),
               prev_start_date: String(metadata?.prev_start_date || ''),
               prev_end_date: String(metadata?.prev_end_date || ''),
               platform: String(metadata?.platform || ''),
               percentage_change: String(metadata?.percentage_change || ''),
            };

            if (metadata?.campaign_id) {
               baseData.campaign_id = String(metadata.campaign_id);
            }
            if (metadata?.campaign_name) {
               baseData.campaign_name = String(metadata.campaign_name);
            }

            if (metadata?.mode) {
               baseData.mode = String(metadata.mode);
            }
            if (metadata?.action_type) {
               baseData.action_type = String(metadata.action_type);
            }
            if (metadata?.source) {
               baseData.source = String(metadata.source);
            }

            return baseData;
         })(),
         session_name: '',
      };
      await addToSessionHistory(addToSessionHistoryPayload);
      await refetchSessionHistory();
      const historyData = await refetchAnalyticshistory();

      dispatch(
         setCurrentHistory({
            agent: 'analytics-agent',
            history: historyData.data || [],
         }),
      );

      const message_history = currentSessionHistory
         ?.sort(
            (a: AnalyticsAgentChat, b: AnalyticsAgentChat) =>
               new Date(a.created_at).getTime() -
               new Date(b.created_at).getTime(),
         )
         .slice(0, 5);

      const context_variables = {
         ...message_history?.[message_history.length - 1]?.context_snapshot,
      };

      if (
         Object.keys(context_variables || {}).length > 0 &&
         Object.keys(context_variables || {}).includes('plan')
      ) {
         delete context_variables.plan;
      }

      const startTime = performance.now();

      const startStreamPayload = {
         text: aiText,
         client_id: client_id as string,
         session_id: sessionId,
         user_id: `${user_id}`,
         message_history:
            message_history
               ?.map((item: AnalyticsAgentChat) => [
                  {
                     role: 'user',
                     content: item.user_query,
                  },
                  {
                     role: 'assistant',
                     content: item.final_response,
                  },
               ])
               .flat() || [],
         context_variables: context_variables,
      };
      const finalPayload =
         currentMode === 'cmo'
            ? {
                 ...startStreamPayload,
                 mode: 'cmo',
              }
            : { ...startStreamPayload };

      if (directResponse && currentMode === 'cmo') {
         const finalResponse = await startDirectResponse(finalPayload);

         if (!finalResponse) {
            const updatedSessionHistoryPayload = {
               ...addToSessionHistoryPayload,
               chat_id: newChatId,
               response_status: 'error' as 'success' | 'error' | 'pending',
            };

            await addToSessionHistory(updatedSessionHistoryPayload);
            await refetchSessionHistory();

            if (kpiMetadata) {
               clearKpiMetadata();
            }

            setQueryRunning(false);
            return;
         }

         const endTime = performance.now();
         const responseTime = Math.round(endTime - startTime);

         const updatedContext = finalResponse?.updated_context;
         const isObject = updatedContext && typeof updatedContext === 'object';

         const updatedSessionHistoryPayload = {
            ...addToSessionHistoryPayload,
            chat_id: newChatId,
            response_status: 'success' as 'success' | 'error' | 'pending',
            final_response: finalResponse.response,
            agent_name: finalResponse?.agent || '',
            prev_agent_response:
               isObject && 'previous_query_result' in updatedContext
                  ? updatedContext.previous_query_result
                  : '',
            prev_agent_name:
               isObject && 'previous_domain' in updatedContext
                  ? updatedContext.previous_domain
                  : '',
            context_snapshot: isObject ? updatedContext : {},
            response_time: responseTime,
            session_name:
               isObject && 'topic' in updatedContext
                  ? updatedContext.topic
                  : displayText,
         };

         const updateFeatureUsagePayload = {
            client_id: client_id || '',
            user_id: user_id || '',
            feature_name: 'analytics_agent',
            feature_type: 'agent',
            mode: currentMode,
         };

         const sendNotificationPayload = {
            client_id: client_id || '',
            user_id: user_id || '',
            notification_title: 'Your analysis is ready.',
            notification_message: `Regarding: "${displayText}"`,
            notification_type: 'analytics_agent',
            notification_data: {
               session_id: sessionId,
               chat_id: newChatId,
               mode: currentMode,
            },
         };

         await addToSessionHistory(updatedSessionHistoryPayload);
         await refetchSessionInsights();
         await refetchSessionHistory();
         await updateFeatureUsage(updateFeatureUsagePayload);
         await new Promise((resolve) => setTimeout(resolve, 300));
         await refetchFeatureUsage();
         const newHistoryData = await refetchAnalyticshistory();

         dispatch(
            setCurrentHistory({
               agent: 'analytics-agent',
               history: newHistoryData.data || [],
            }),
         );

         if (kpiMetadata) {
            clearKpiMetadata();
         }

         if (currentMode === 'cmo' || routedFromAgent === 'diagnostic-agent') {
            await sendNotification(sendNotificationPayload);

            await queryClient.invalidateQueries({
               queryKey: ['notifications'],
            });

            if (notificationPermission === 'granted') {
               new Notification('Analysis Complete!', {
                  body: 'Your deep analysis is complete. Please check the results.',
               });
            }

            toast.success(
               'Deep analysis is complete. Please check the results.',
            );
         }

         dispatch(setChunks([]));
         setRoutedFrom(null);
         setQueryRunning(false);

         return;
      }

      const allChunks = await startStream(finalPayload);

      const endTime = performance.now();
      const responseTime = Math.round(endTime - startTime);

      if (allChunks?.length === 0) {
         const updatedSessionHistoryPayload = {
            ...addToSessionHistoryPayload,
            chat_id: newChatId,
            response_status: 'error' as 'success' | 'error' | 'pending',
            response_time: responseTime,
         };

         await addToSessionHistory(updatedSessionHistoryPayload);
         await refetchSessionHistory();

         if (kpiMetadata) {
            clearKpiMetadata();
         }

         setQueryRunning(false);
         return;
      }

      if (!allChunks.some((chunk) => chunk.type === 'thought')) {
         const updatedSessionHistoryPayload = {
            ...addToSessionHistoryPayload,
            chat_id: newChatId,
            response_status: 'aborted' as
               | 'success'
               | 'error'
               | 'pending'
               | 'aborted',
            response_time: responseTime,
         };

         await addToSessionHistory(updatedSessionHistoryPayload);
         await refetchSessionHistory();

         if (kpiMetadata) {
            clearKpiMetadata();
         }

         const chatInsights = {
            client_id: client_id as string,
            user_id: user_id as string,
            session_id: sessionId,
            chat_id: newChatId,
            chat_flow_context: [...allChunks],
         };

         await addInsightsToSessionHistory(chatInsights);
         await refetchSessionInsights();

         setQueryRunning(false);
         return;
      }

      if (!allChunks.some((chunk) => chunk.type === 'final_result')) {
         const content = allChunks
            .filter((chunk) => chunk.type === 'thought')
            .map((chunk) => chunk.content)
            .join('');

         const updatedSessionHistoryPayload = {
            ...addToSessionHistoryPayload,
            chat_id: newChatId,
            response_status: 'aborted' as 'success' | 'error' | 'pending',
            response_time: responseTime,
            final_response: content,
         };

         await addToSessionHistory(updatedSessionHistoryPayload);
         await refetchSessionHistory();

         if (kpiMetadata) {
            clearKpiMetadata();
         }

         const chatInsights = {
            client_id: client_id as string,
            user_id: user_id as string,
            session_id: sessionId,
            chat_id: newChatId,
            chat_flow_context: [...allChunks],
         };

         await addInsightsToSessionHistory(chatInsights);
         await refetchSessionInsights();

         setQueryRunning(false);
         return;
      }

      const finalChunk = allChunks?.find(
         (chunk) => chunk.status === 'completed',
      );

      const chatInsights = {
         client_id: client_id as string,
         user_id: user_id as string,
         session_id: sessionId,
         chat_id: newChatId,
         chat_flow_context: [...allChunks],
      };

      const updatedContext = finalChunk?.metadata?.updated_context;
      const isObject = updatedContext && typeof updatedContext === 'object';

      const updatedSessionHistoryPayload = {
         ...addToSessionHistoryPayload,
         chat_id: newChatId,
         response_status: 'success' as 'success' | 'error' | 'pending',
         final_response:
            finalChunk?.content === undefined
               ? ERROR_MESSAGES[
                    Math.floor(Math.random() * ERROR_MESSAGES.length)
                 ]
               : typeof finalChunk?.content === 'string'
                 ? finalChunk.content
                 : JSON.stringify(finalChunk?.content || ''),
         agent_name: finalChunk?.agent_name || '',
         prev_agent_response:
            isObject && 'previous_query_result' in updatedContext
               ? updatedContext.previous_query_result
               : '',
         prev_agent_name:
            isObject && 'previous_domain' in updatedContext
               ? updatedContext.previous_domain
               : '',
         context_snapshot: isObject ? updatedContext : {},
         response_time: responseTime,
         session_name:
            isObject && 'topic' in updatedContext
               ? updatedContext.topic
               : displayText,
      };

      const updateFeatureUsagePayload = {
         client_id: client_id || '',
         user_id: user_id || '',
         feature_name: 'analytics_agent',
         feature_type: 'agent',
         mode: currentMode,
      };

      const sendNotificationPayload = {
         client_id: client_id || '',
         user_id: user_id || '',
         notification_title: 'Your analysis is ready.',
         notification_message: `Regarding: "${displayText}"`,
         notification_type: 'analytics_agent',
         notification_data: {
            session_id: sessionId,
            chat_id: newChatId,
            mode: currentMode,
         },
      };

      await addInsightsToSessionHistory(chatInsights);
      await addToSessionHistory(updatedSessionHistoryPayload);
      await refetchSessionInsights();
      await refetchSessionHistory();
      await updateFeatureUsage(updateFeatureUsagePayload);
      await new Promise((resolve) => setTimeout(resolve, 300));
      await refetchFeatureUsage();
      const newHistoryData = await refetchAnalyticshistory();

      dispatch(
         setCurrentHistory({
            agent: 'analytics-agent',
            history: newHistoryData.data || [],
         }),
      );

      if (kpiMetadata) {
         clearKpiMetadata();
      }

      if (currentMode === 'cmo' || routedFromAgent === 'diagnostic-agent') {
         await sendNotification(sendNotificationPayload);

         await queryClient.invalidateQueries({
            queryKey: ['notifications'],
         });

         if (notificationPermission === 'granted') {
            new Notification('Analysis Complete!', {
               body: 'Your deep analysis is complete. Please check the results.',
            });
         }

         toast.success('Deep analysis is complete. Please check the results.');
      }

      dispatch(setChunks([]));
      setRoutedFrom(null);
      setQueryRunning(false);
   };

   const handleSampleQuestionClick = async (question: string) => {
      await handleSendPrompt(question).catch(console.log);
   };

   useEffect(() => {
      setTimeout(() => {
         messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 0);
   }, [chunks, currentSessionHistory]);

   useEffect(() => {
      currentMode === 'cmo'
         ? setSuggestions(CMO_QUESTION_SUGGESTIONS)
         : setSuggestions(
              getSmartSuggestions(
                 connectionDetails,
                 DEFAULT_QUESTION_SUGGESTIONS,
              ),
           );
   }, [currentMode, connectionDetails]);

   useEffect(() => {
      const kpiMetadata = getKpiMetadata();

      if (
         kpiMetadata &&
         kpiDisplayPrompt &&
         kpiAiPrompt &&
         !kpiPromptsProcessed
      ) {
         dispatch(setKpiPromptsProcessed(true));

         if (
            routedFromAgent &&
            routedFromAgent.startsWith('diagnostic-agent/')
         ) {
            setRoutedFrom('diagnostic-agent');
         } else {
            setRoutedFrom(
               routedFromAgent as 'alerting-agent' | 'diagnostic-agent' | null,
            );
         }
         handleSendPrompt(kpiDisplayPrompt, kpiAiPrompt, 'diagnostic-agent')
            .catch(console.log)
            .finally(() => {
               dispatch(clearKpiPrompts());
            });
      } else if (paramsQuery || paramsAiPrompt) {
         setRoutedFrom('alerting-agent');
         handleSendPrompt(paramsQuery, paramsAiPrompt, 'alerting-agent')
            .catch(console.log)
            .finally(() => {
               const url = new URL(window.location.href);
               url.search = '';
               window.history.replaceState({}, document.title, url.toString());
            });
      }
   }, [
      paramsQuery,
      paramsAiPrompt,
      kpiDisplayPrompt,
      kpiAiPrompt,
      kpiPromptsProcessed,
      routedFromAgent,
   ]);

   useEffect(() => {
      return () => {
         sessionStorage.removeItem('kpi_diagnostics_metadata');
      };
   }, []);

   useEffect(() => {
      const handleBeforeUnload = (event: BeforeUnloadEvent) => {
         if (!queryRunning) return;

         event.preventDefault();
         event.returnValue = '';
      };

      window.addEventListener('beforeunload', handleBeforeUnload);

      return () => {
         window.removeEventListener('beforeunload', handleBeforeUnload);
      };
   }, [queryRunning]);

   return (
      <div
         className={`w-full h-full flex flex-col items-center ${!currentSessionHistory || currentSessionHistory.length < 1 ? 'justify-center' : ''}`}
      >
         <div
            className={`scrollbaar-hidden w-[95%] max-w-[900px] flex flex-col items-center gap-[5px] overflow-auto pt-[0px] md:pt-[20px] ${currentSessionHistory && currentSessionHistory.length > 0 ? 'h-full' : ''}`}
         >
            {(!currentSessionHistory ||
               currentSessionHistory?.length === 0) && (
               <>
                  <div className='w-40 h-40 overflow-hidden'>
                     <img
                        src={
                           currentMode === 'cmo'
                              ? aiCMOAgentIcon
                              : analyticAgentIcon
                        }
                        alt='cropped'
                        className='w-36'
                     />
                  </div>
                  <p className='w-full text-center font-bold text-[16px] md:text-[20px] text-black p-[10px] max-w-[750px]'>
                     {currentMode === 'cmo'
                        ? 'Analytics Agent - AI CMO'
                        : 'Analytics Agent'}
                  </p>
                  <div className='w-full text-center text-[12px] md:text-[16px] text-black p-[10px] max-w-[500px]'>
                     <p>
                        I'm your{' '}
                        <span className='font-bold'>
                           {currentMode === 'cmo'
                              ? 'AI CMO agent'
                              : 'Analytics Agent'}
                        </span>{' '}
                        , trained to dive deep into your data, cut through the
                        clutter, spot what's driving results, and make smarter,
                        faster decisions with confidence.
                     </p>
                     <div className='text-center text-[12px] md:text-[14px] text-black mt-3 md:mt-5 mb-2'>
                        👉 Try a sample question to get started.
                     </div>
                  </div>
                  <div className='flex flex-wrap gap-2 items-center justify-center max-w-[900px] text-[12px] md:text-[14px] mx-4 mb-10'>
                     {suggestions.map((question, idx) => (
                        <React.Fragment key={idx}>
                           <Tooltips
                              content={question}
                              className='bg-gray-500 text-white w-max max-w-[400px] fit-c para5'
                           >
                              <div
                                 className='flex-1 w-full md:w-[180px] md:min-h-[95px] max-h-[95px] border-[1px] border-gray-300 text-left rounded-[15px] px-[15px] py-[5px] cursor-pointer shadow-md font-semibold bg-white hover:bg-gray-100 hover:text-black line-clamp-4 overflow-hidden text-ellipsis'
                                 onClick={() =>
                                    void handleSampleQuestionClick(question)
                                 }
                              >
                                 {question}
                              </div>
                           </Tooltips>
                        </React.Fragment>
                     ))}
                  </div>
               </>
            )}
            {currentSessionHistory && currentSessionHistory.length > 0 && (
               <div className='w-full flex-1 justify-center overflow-y-auto'>
                  <div className='w-full flex flex-col justify-start max-w-[950px] overflow-auto'>
                     <div className='message'>
                        <AnalyticAgentChat
                           chunks={chunks}
                           sessionInsights={sessionInsights || []}
                           currentSessionChats={currentSessionHistory}
                           handleSendPrompt={handleSendPrompt}
                           routedFrom={routedFrom}
                        />
                        <div ref={messagesEndRef}></div>
                     </div>
                  </div>
               </div>
            )}
         </div>
         <div className='flex flex-col items-center w-[95%] max-w-[900px] my-[10px] md:my-[20px] flex-nowrap justify-between gap-2 bg-white'>
            <div className='w-full shadow-md rounded-xl border-2'>
               <Textarea
                  rows={1}
                  placeholder='Ask about trends, metrics, or performance...'
                  className='resize-none max-h-[150px] overflow-auto !text-[12px] md:!text-[16px] shadow-none border-none focus:ring-0 focus:border-none :hover:border-none focus-visible:border-0 focus-visible:ring-0 focus-visible:outline-none'
                  value={prompt}
                  onKeyDown={(e) => {
                     if (
                        !(
                           isFetchingSessionHistory ||
                           isFetchingSessionInsights ||
                           isStreaming ||
                           isPendingAddToSessionHistory ||
                           isPendingAddInsightsToSessionHistory
                        )
                     ) {
                        void handleKeyDown(e);
                     }
                  }}
                  onChange={handlePromptChange}
               />
               <div className='flex items-center justify-between rounded-xl p-2 bg-white'>
                  <Tabs
                     defaultValue={currentMode}
                     value={currentMode}
                     onValueChange={(value) => {
                        dispatch(
                           setCurrentMode(value as 'data-analyst' | 'cmo'),
                        );
                     }}
                  >
                     <TabsList
                        className={cn(
                           defaultTabListClass,
                           'h-[45px] w-[80px] rounded-md bg-[#E4F2FF] gap-[3px]',
                        )}
                     >
                        <HoverCard openDelay={200} closeDelay={200}>
                           <HoverCardTrigger className='w-[35px] h-[35px] p-0'>
                              <TabsTrigger
                                 value='data-analyst'
                                 className={cn(
                                    defaultTabTriggerClass,
                                    'rounded-md w-full hover:cursor-pointer data-[state=inactive]:border-none data-[state=active]:!border-[#3C76E1] data-[state=active]:!bg-[#ffffff]',
                                 )}
                              >
                                 <PiChartBar color='#3C76E1' />
                              </TabsTrigger>
                           </HoverCardTrigger>
                           <HoverCardContent className='bg-black w-[300px] text-white'>
                              <p className='text-md'>Data Analyst ~ 30secs</p>
                              <Separator className='my-2' />
                              <p className='text-sm'>
                                 Focuses on analyzing single-domain data to
                                 generate actionable insights and
                                 recommendations.
                              </p>
                              <p className='text-xs mt-1 text-gray-400'>
                                 {USAGE_LIMITS.data_analyst -
                                    ((featureUsage &&
                                       featureUsage?.find(
                                          (obj) => obj.mode === 'data-analyst',
                                       )?.no_of_calls) ||
                                       0)}{' '}
                                 remaining in the free trial.
                              </p>
                              <HoverCardArrow className='fill-black' />
                           </HoverCardContent>
                        </HoverCard>

                        <HoverCard openDelay={200} closeDelay={200}>
                           <HoverCardTrigger className='w-[35px] h-[35px] p-0'>
                              <TabsTrigger
                                 value='cmo'
                                 className={cn(
                                    defaultTabTriggerClass,
                                    'rounded-md w-full p-2',
                                    'hover:cursor-pointer data-[state=inactive]:border-none data-[state=active]:!border-[#3C76E1] data-[state=active]:!bg-[#ffffff]',
                                 )}
                              >
                                 <div className='w-[20px] h-[20px] block'>
                                    <img
                                       src={CMOModeIcon}
                                       alt='CMO Mode'
                                       className='w-[20px] h-[20px] block'
                                    />
                                 </div>
                              </TabsTrigger>
                           </HoverCardTrigger>
                           <HoverCardContent className='bg-black w-[300px] text-white'>
                              <p className='text-md'>CMO ~ 5mins</p>
                              <Separator className='my-2' />
                              <p className='text-sm'>
                                 Plans, analyzes, and diagnoses cross-domain
                                 performance to uncover what's working—and
                                 what's not. Delivers strategic{' '}
                                 <strong>prescriptions</strong> to help you act
                                 faster and grow smarter.
                              </p>
                              <p className='text-xs mt-1 text-gray-400'>
                                 {USAGE_LIMITS.cmo -
                                    ((featureUsage &&
                                       featureUsage?.find(
                                          (obj) => obj.mode === 'cmo',
                                       )?.no_of_calls) ||
                                       0)}{' '}
                                 remaining in the free trial.
                              </p>
                              <HoverCardArrow className='fill-black' />
                           </HoverCardContent>
                        </HoverCard>
                     </TabsList>
                  </Tabs>
                  <Button
                     disabled={
                        isFetchingSessionHistory ||
                        isFetchingSessionInsights ||
                        isStreaming ||
                        isPendingAddToSessionHistory ||
                        isPendingAddInsightsToSessionHistory
                     }
                     onClick={() => void handleSendPrompt()}
                     className='w-[40px] h-[40px] bg-[#437eeb] text-white rounded-md shadow-md hover:bg-[#3c76e1] disabled:opacity-50 hover:cursor-pointer'
                  >
                     <IoAnalyticsSharp style={{ height: 24, width: 24 }} />
                  </Button>
               </div>
            </div>
            <p className='text-[10px] md:text-[12px] text-gray-500 text-center'>
               Agent can make mistakes. Please double-check responses.
            </p>
         </div>
      </div>
   );
};

export default AnalyticsAgent;
