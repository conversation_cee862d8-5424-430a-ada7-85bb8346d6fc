import { StreamChunk } from '@/api/service/agentic-workflow/analytics-agent';
import { PayloadAction, createSlice } from '@reduxjs/toolkit';

interface InitialState {
   currentMode: 'data-analyst' | 'cmo';
   currentSessionID: string;
   chunks: StreamChunk[];
   currentPage: number;
   notificationPermission: NotificationPermission;
   kpiDisplayPrompt: string;
   kpiAiPrompt: string;
   kpiPromptsProcessed: boolean;
   directResponse: boolean;
}

const analyticsAgentState: InitialState = {
   currentSessionID: '',
   currentMode: 'data-analyst',
   chunks: [],
   currentPage: 1,
   notificationPermission: 'default',
   kpiDisplayPrompt: '',
   kpiAiPrompt: '',
   kpiPromptsProcessed: false,
   directResponse: true,
};

const analyticsAgentSlice = createSlice({
   name: 'analyticsAgent',
   initialState: analyticsAgentState,
   reducers: {
      setCurrentSessionID: (state, action: PayloadAction<string>) => {
         state.currentSessionID = action.payload;
      },
      setCurrentMode: (
         state,
         action: PayloadAction<'data-analyst' | 'cmo'>,
      ) => {
         state.currentMode = action.payload;
      },
      setChunks: (state, action: PayloadAction<StreamChunk[]>) => {
         state.chunks = action.payload;
      },
      addChunk: (state, action: PayloadAction<StreamChunk>) => {
         state.chunks.push(action.payload);
      },
      setCurrentPage: (state, action: PayloadAction<number>) => {
         state.currentPage = action.payload;
      },
      setNotificationPermission: (
         state,
         action: PayloadAction<NotificationPermission>,
      ) => {
         state.notificationPermission = action.payload;
      },
      setKpiPrompts: (
         state,
         action: PayloadAction<{ displayPrompt: string; aiPrompt: string }>,
      ) => {
         state.kpiDisplayPrompt = action.payload.displayPrompt;
         state.kpiAiPrompt = action.payload.aiPrompt;
         state.kpiPromptsProcessed = false;
      },
      clearKpiPrompts: (state) => {
         state.kpiDisplayPrompt = '';
         state.kpiAiPrompt = '';
         state.kpiPromptsProcessed = false;
      },
      setKpiPromptsProcessed: (state, action: PayloadAction<boolean>) => {
         state.kpiPromptsProcessed = action.payload;
      },
      setDirectResponse: (state, action: PayloadAction<boolean>) => {
         state.directResponse = action.payload;
      },
   },
});

export const {
   setCurrentSessionID,
   setCurrentMode,
   setChunks,
   addChunk,
   setCurrentPage,
   setNotificationPermission,
   setKpiPrompts,
   clearKpiPrompts,
   setKpiPromptsProcessed,
   setDirectResponse,
} = analyticsAgentSlice.actions;

export default analyticsAgentSlice.reducer;
